# 全局使用规则

## 📋 强制性规则 (Mandatory Rules)

### 🔧 MCP工具使用强制令

#### 所有操作必须使用对应的MCP工具，禁止直接调用任何标准库函数

| 操作类型 | 必须使用的MCP工具 | 严格禁止 |
|---------|-----------------|---------|
| 文件读写操作 | `filesystem` | `open()`, `os.path.*`, `shutil.*` |
| 代码搜索分析 | `codebase_search` | `grep`, `find`, `ast.parse` |
| 浏览器网页操作 | `playwright` | `requests.*`, `urllib.*`, `selenium` |
| 系统命令执行 | `desktop-commander` | `subprocess.*`, `os.system()` |
| 复杂思考推理 | `sequential-thinking` | 直接逻辑处理 |
| 文档查询检索 | `context7` | 手动搜索文档 |
| 数据存储记忆 | `memory` | 直接变量存储 |
| 网络数据采集 | `brightdata` | 直接爬虫代码 |
| 综合多功能 | `everything` | 混合操作 |

### 🔤 中文编码强制令

#### 所有输出必须支持中文，禁止乱码

- 文件编码：强制使用 UTF-8
- 注释语言：强制使用中文
- 函数说明：强制使用中文
- 错误信息：强制使用中文
- 日志输出：强制使用中文
- 控制台显示：强制支持中文

### 🎯 任务执行强制令

#### 每个用户请求必须转换为具体可执行任务

- 禁止模糊回复：必须明确具体操作步骤
- 禁止跳过验证：必须验证功能完整性
- 禁止保留测试文件：必须自动清理所有临时文件
- 禁止增量执行：必须完整执行所有步骤

### 🚫 代码质量强制令

#### 绝对禁止以下行为

❌ **禁止简化代码**

- 不允许 `pass` 占位符
- 不允许 `TODO` 标记
- 不允许 `...` 省略
- 不允许空函数实现

❌ **禁止虚假功能**

- 不允许模拟数据库连接
- 不允许假装网络请求
- 不允许演示性代码
- 不允许测试桩代码

❌ **禁止无关建议**

- 不允许推荐与项目无关的功能
- 不允许建议架构重构
- 不允许提出设计模式建议
- 不允许添加项目外的依赖

### ✅ 代码质量要求令

#### 所有代码必须符合以下标准

✅ **真实功能实现**

- 必须有完整的错误处理
- 必须有详细的中文注释
- 必须有实际的业务逻辑
- 必须能立即运行使用

✅ **项目相关性**

- 必须基于项目真实代码结构
- 必须使用项目现有依赖
- 必须符合项目编码规范
- 必须解决项目实际问题

### 🧹 清理机制强制令

#### 执行完毕后必须自动清理

- 删除所有测试文件
- 删除所有临时目录
- 删除所有调试代码
- 删除所有演示数据

### 📊 验证机制强制令

#### 每次操作都必须验证

- 功能完整性验证
- 中文编码支持验证
- MCP工具使用验证
- 代码质量标准验证
- 清理完成状态验证

---

## ⚠️ 违规处理机制

### 🚨 立即终止情况

以下行为将导致操作立即终止：

1. 未使用对应的MCP工具
2. 使用了被禁止的标准库函数
3. 创建了简化或虚假的代码
4. 输出了乱码或不支持中文
5. 未完成清理操作

### 🔍 合规检查清单

每次操作前必须确认：

- [ ] 已选择正确的MCP工具
- [ ] 已设置UTF-8编码
- [ ] 已准备中文注释模板
- [ ] 已明确任务执行步骤

每次操作中必须确认：

- [ ] 正在使用对应的MCP工具
- [ ] 代码包含完整功能实现
- [ ] 注释和输出使用中文
- [ ] 记录了需要清理的文件

每次操作后必须确认：

- [ ] 功能验证通过
- [ ] 中文显示正常
- [ ] 已清理所有测试文件
- [ ] MCP工具使用记录完整

---

## 🎯 执行优先级

### 第一优先级：MCP工具强制使用

- 任何操作都不允许绕过MCP工具
- 发现直接调用标准库立即终止

### 第二优先级：中文编码支持

- 任何乱码输出立即修正
- 所有注释必须使用中文

### 第三优先级：功能完整性

- 不允许任何简化实现
- 必须提供完整可用的功能

### 第四优先级：自动清理

- 执行完毕立即清理
- 不允许遗留任何测试文件

---

## 📝 简化版执行流程

1. **接收用户请求** → 转换为具体任务
2. **选择MCP工具** → 验证工具可用性
3. **执行具体操作** → 使用对应MCP工具
4. **验证执行结果** → 确认功能完整
5. **自动清理文件** → 删除所有临时文件
6. **提交最终结果** → 确保中文正常显示

---

**此规则为全局强制执行标准，适用于Cursor IDE中的所有MCP工具操作，违反者将被立即终止执行。**
