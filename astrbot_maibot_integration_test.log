2025-08-16 20:36:47,773 - asyncio - DEBUG - Using proactor: IocpProactor
2025-08-16 20:36:47,775 - __main__ - INFO - 生产级集成测试器初始化完成
2025-08-16 20:36:47,775 - __main__ - INFO - 开始运行完整的生产级集成测试套件
2025-08-16 20:36:47,775 - __main__ - INFO - 开始测试: dynamic_path_detection
2025-08-16 20:36:48,006 - astrbot.core.maibot_integration - INFO - MaiBot源码路径检测成功: E:\MaiBotOne\modules\MaiBot\src
2025-08-16 20:36:48,008 - astrbot.core.maibot_integration - INFO - MaiBot模块验证 - 存在: 9, 缺失: 0
2025-08-16 20:36:48,008 - astrbot.core.maibot_integration - INFO - 开始导入MaiBot核心模块...
2025-08-16 20:36:48,018 - astrbot.core.maibot_integration - ERROR - MaiBot集成初始化失败: No module named 'src'
Traceback (most recent call last):
  File "E:\MaiBotOne\modules\MaiBot\plugins\AstrBot\astrbot\core\maibot_integration.py", line 127, in <module>
    from plugin_system.base.base_plugin import BasePlugin
  File "E:\MaiBotOne\modules\MaiBot\src\plugin_system\__init__.py", line 8, in <module>
    from .base import (
  File "E:\MaiBotOne\modules\MaiBot\src\plugin_system\base\__init__.py", line 7, in <module>
    from .base_plugin import BasePlugin
  File "E:\MaiBotOne\modules\MaiBot\src\plugin_system\base\base_plugin.py", line 3, in <module>
    from .plugin_base import PluginBase
  File "E:\MaiBotOne\modules\MaiBot\src\plugin_system\base\plugin_base.py", line 10, in <module>
    from src.common.logger import get_logger
ModuleNotFoundError: No module named 'src'
2025-08-16 20:36:48,028 - __main__ - INFO - 路径检测测试完成: True
2025-08-16 20:36:48,028 - __main__ - INFO - 开始测试: maibot_module_import
2025-08-16 20:36:48,028 - __main__ - INFO - MaiBot模块导入测试完成: False
2025-08-16 20:36:48,028 - __main__ - INFO - 开始测试: astrbot_core_initialization
2025-08-16 20:36:48,853 - astrbot - DEBUG - 平台适配器 webchat 已注册
2025-08-16 20:36:49,155 - __main__ - ERROR - 日志系统初始化失败: LogBroker.__init__() takes 1 positional argument but 2 were given
2025-08-16 20:36:49,155 - __main__ - ERROR - 数据库初始化失败: 'SQLiteDatabase' object has no attribute 'get_db'
2025-08-16 20:36:49,155 - __main__ - INFO - AstrBot核心初始化测试完成: False (0/3)
2025-08-16 20:36:49,155 - __main__ - INFO - 开始测试: integration_manager
2025-08-16 20:36:49,157 - astrbot.core.maibot_integration - ERROR - MaiBot集成不可用，无法初始化集成管理器
2025-08-16 20:36:49,157 - astrbot.core.maibot_integration - ERROR - MaiBot集成不可用，跳过初始化
2025-08-16 20:36:49,157 - __main__ - INFO - 集成管理器初始化: False
2025-08-16 20:36:49,157 - __main__ - INFO - 集成状态获取成功
2025-08-16 20:36:49,157 - astrbot.core.maibot_integration - DEBUG - 处理MaiBot请求: test_request
2025-08-16 20:36:49,157 - astrbot.core.maibot_integration - ERROR - 处理MaiBot请求失败: 'MaiBotIntegrationManager' object has no attribute 'message_converter'
Traceback (most recent call last):
  File "E:\MaiBotOne\modules\MaiBot\plugins\AstrBot\astrbot\core\maibot_integration.py", line 401, in process_maibot_request
    astrbot_message = await self.message_converter.convert_from_maibot(request_data)
                            ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'MaiBotIntegrationManager' object has no attribute 'message_converter'
2025-08-16 20:36:49,158 - __main__ - INFO - 请求处理测试成功
2025-08-16 20:36:49,158 - __main__ - INFO - 集成管理器测试完成: False (2/3)
2025-08-16 20:36:49,158 - __main__ - INFO - 开始测试: llm_providers_integration
2025-08-16 20:36:49,158 - __main__ - INFO - 开始测试: pipeline_processing
2025-08-16 20:36:49,158 - __main__ - INFO - 开始测试: bidirectional_control
2025-08-16 20:36:49,158 - __main__ - INFO - 开始测试: performance_monitoring
