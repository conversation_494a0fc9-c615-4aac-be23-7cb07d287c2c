Metadata-Version: 2.4
Name: lia-web
Version: 0.2.3
Summary: A library for working with web frameworks
Author-email: <PERSON> <<EMAIL>>
License-File: LICENSE
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Requires-Python: >=3.9
Requires-Dist: typing-extensions>=4.14.0
Description-Content-Type: text/markdown

# Lia

**Write once, run everywhere** - A universal web framework adapter for Python that lets you write code once and use it across multiple web frameworks.

## Installation

```bash
uv add lia-web
```

## Overview

Lia provides a unified interface for common web framework operations, allowing you to write framework-agnostic code that can be easily adapted to work with FastAPI, Flask, Django, and other popular Python web frameworks.

## Features

This project is in early development!
