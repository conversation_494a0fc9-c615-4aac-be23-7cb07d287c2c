Metadata-Version: 2.4
Name: pymongo
Version: 4.14.0
Summary: PyMongo - the Official MongoDB Python driver
Project-URL: Homepage, https://www.mongodb.org
Project-URL: Documentation, https://www.mongodb.com/docs/languages/python/pymongo-driver/current/
Project-URL: Source, https://github.com/mongodb/mongo-python-driver
Project-URL: Tracker, https://jira.mongodb.org/projects/PYTHON/issues
Author: The MongoDB Python Team
License:                                  Apache License
                                   Version 2.0, January 2004
                                http://www.apache.org/licenses/
        
           TERMS AND CONDITIONS FOR USE, REPRODUCTION, AND DISTRIBUTION
        
           1. Definitions.
        
              "License" shall mean the terms and conditions for use, reproduction,
              and distribution as defined by Sections 1 through 9 of this document.
        
              "Licensor" shall mean the copyright owner or entity authorized by
              the copyright owner that is granting the License.
        
              "Legal Entity" shall mean the union of the acting entity and all
              other entities that control, are controlled by, or are under common
              control with that entity. For the purposes of this definition,
              "control" means (i) the power, direct or indirect, to cause the
              direction or management of such entity, whether by contract or
              otherwise, or (ii) ownership of fifty percent (50%) or more of the
              outstanding shares, or (iii) beneficial ownership of such entity.
        
              "You" (or "Your") shall mean an individual or Legal Entity
              exercising permissions granted by this License.
        
              "Source" form shall mean the preferred form for making modifications,
              including but not limited to software source code, documentation
              source, and configuration files.
        
              "Object" form shall mean any form resulting from mechanical
              transformation or translation of a Source form, including but
              not limited to compiled object code, generated documentation,
              and conversions to other media types.
        
              "Work" shall mean the work of authorship, whether in Source or
              Object form, made available under the License, as indicated by a
              copyright notice that is included in or attached to the work
              (an example is provided in the Appendix below).
        
              "Derivative Works" shall mean any work, whether in Source or Object
              form, that is based on (or derived from) the Work and for which the
              editorial revisions, annotations, elaborations, or other modifications
              represent, as a whole, an original work of authorship. For the purposes
              of this License, Derivative Works shall not include works that remain
              separable from, or merely link (or bind by name) to the interfaces of,
              the Work and Derivative Works thereof.
        
              "Contribution" shall mean any work of authorship, including
              the original version of the Work and any modifications or additions
              to that Work or Derivative Works thereof, that is intentionally
              submitted to Licensor for inclusion in the Work by the copyright owner
              or by an individual or Legal Entity authorized to submit on behalf of
              the copyright owner. For the purposes of this definition, "submitted"
              means any form of electronic, verbal, or written communication sent
              to the Licensor or its representatives, including but not limited to
              communication on electronic mailing lists, source code control systems,
              and issue tracking systems that are managed by, or on behalf of, the
              Licensor for the purpose of discussing and improving the Work, but
              excluding communication that is conspicuously marked or otherwise
              designated in writing by the copyright owner as "Not a Contribution."
        
              "Contributor" shall mean Licensor and any individual or Legal Entity
              on behalf of whom a Contribution has been received by Licensor and
              subsequently incorporated within the Work.
        
           2. Grant of Copyright License. Subject to the terms and conditions of
              this License, each Contributor hereby grants to You a perpetual,
              worldwide, non-exclusive, no-charge, royalty-free, irrevocable
              copyright license to reproduce, prepare Derivative Works of,
              publicly display, publicly perform, sublicense, and distribute the
              Work and such Derivative Works in Source or Object form.
        
           3. Grant of Patent License. Subject to the terms and conditions of
              this License, each Contributor hereby grants to You a perpetual,
              worldwide, non-exclusive, no-charge, royalty-free, irrevocable
              (except as stated in this section) patent license to make, have made,
              use, offer to sell, sell, import, and otherwise transfer the Work,
              where such license applies only to those patent claims licensable
              by such Contributor that are necessarily infringed by their
              Contribution(s) alone or by combination of their Contribution(s)
              with the Work to which such Contribution(s) was submitted. If You
              institute patent litigation against any entity (including a
              cross-claim or counterclaim in a lawsuit) alleging that the Work
              or a Contribution incorporated within the Work constitutes direct
              or contributory patent infringement, then any patent licenses
              granted to You under this License for that Work shall terminate
              as of the date such litigation is filed.
        
           4. Redistribution. You may reproduce and distribute copies of the
              Work or Derivative Works thereof in any medium, with or without
              modifications, and in Source or Object form, provided that You
              meet the following conditions:
        
              (a) You must give any other recipients of the Work or
                  Derivative Works a copy of this License; and
        
              (b) You must cause any modified files to carry prominent notices
                  stating that You changed the files; and
        
              (c) You must retain, in the Source form of any Derivative Works
                  that You distribute, all copyright, patent, trademark, and
                  attribution notices from the Source form of the Work,
                  excluding those notices that do not pertain to any part of
                  the Derivative Works; and
        
              (d) If the Work includes a "NOTICE" text file as part of its
                  distribution, then any Derivative Works that You distribute must
                  include a readable copy of the attribution notices contained
                  within such NOTICE file, excluding those notices that do not
                  pertain to any part of the Derivative Works, in at least one
                  of the following places: within a NOTICE text file distributed
                  as part of the Derivative Works; within the Source form or
                  documentation, if provided along with the Derivative Works; or,
                  within a display generated by the Derivative Works, if and
                  wherever such third-party notices normally appear. The contents
                  of the NOTICE file are for informational purposes only and
                  do not modify the License. You may add Your own attribution
                  notices within Derivative Works that You distribute, alongside
                  or as an addendum to the NOTICE text from the Work, provided
                  that such additional attribution notices cannot be construed
                  as modifying the License.
        
              You may add Your own copyright statement to Your modifications and
              may provide additional or different license terms and conditions
              for use, reproduction, or distribution of Your modifications, or
              for any such Derivative Works as a whole, provided Your use,
              reproduction, and distribution of the Work otherwise complies with
              the conditions stated in this License.
        
           5. Submission of Contributions. Unless You explicitly state otherwise,
              any Contribution intentionally submitted for inclusion in the Work
              by You to the Licensor shall be under the terms and conditions of
              this License, without any additional terms or conditions.
              Notwithstanding the above, nothing herein shall supersede or modify
              the terms of any separate license agreement you may have executed
              with Licensor regarding such Contributions.
        
           6. Trademarks. This License does not grant permission to use the trade
              names, trademarks, service marks, or product names of the Licensor,
              except as required for reasonable and customary use in describing the
              origin of the Work and reproducing the content of the NOTICE file.
        
           7. Disclaimer of Warranty. Unless required by applicable law or
              agreed to in writing, Licensor provides the Work (and each
              Contributor provides its Contributions) on an "AS IS" BASIS,
              WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
              implied, including, without limitation, any warranties or conditions
              of TITLE, NON-INFRINGEMENT, MERCHANTABILITY, or FITNESS FOR A
              PARTICULAR PURPOSE. You are solely responsible for determining the
              appropriateness of using or redistributing the Work and assume any
              risks associated with Your exercise of permissions under this License.
        
           8. Limitation of Liability. In no event and under no legal theory,
              whether in tort (including negligence), contract, or otherwise,
              unless required by applicable law (such as deliberate and grossly
              negligent acts) or agreed to in writing, shall any Contributor be
              liable to You for damages, including any direct, indirect, special,
              incidental, or consequential damages of any character arising as a
              result of this License or out of the use or inability to use the
              Work (including but not limited to damages for loss of goodwill,
              work stoppage, computer failure or malfunction, or any and all
              other commercial damages or losses), even if such Contributor
              has been advised of the possibility of such damages.
        
           9. Accepting Warranty or Additional Liability. While redistributing
              the Work or Derivative Works thereof, You may choose to offer,
              and charge a fee for, acceptance of support, warranty, indemnity,
              or other liability obligations and/or rights consistent with this
              License. However, in accepting such obligations, You may act only
              on Your own behalf and on Your sole responsibility, not on behalf
              of any other Contributor, and only if You agree to indemnify,
              defend, and hold each Contributor harmless for any liability
              incurred by, or claims asserted against, such Contributor by reason
              of your accepting any such warranty or additional liability.
        
           END OF TERMS AND CONDITIONS
        
           APPENDIX: How to apply the Apache License to your work.
        
              To apply the Apache License to your work, attach the following
              boilerplate notice, with the fields enclosed by brackets "[]"
              replaced with your own identifying information. (Don't include
              the brackets!)  The text should be enclosed in the appropriate
              comment syntax for the file format. We also recommend that a
              file or class name and description of purpose be included on the
              same "printed page" as the copyright notice for easier
              identification within third-party archives.
        
           Copyright [yyyy] [name of copyright owner]
        
           Licensed under the Apache License, Version 2.0 (the "License");
           you may not use this file except in compliance with the License.
           You may obtain a copy of the License at
        
               http://www.apache.org/licenses/LICENSE-2.0
        
           Unless required by applicable law or agreed to in writing, software
           distributed under the License is distributed on an "AS IS" BASIS,
           WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
           See the License for the specific language governing permissions and
           limitations under the License.
License-File: LICENSE
Keywords: bson,gridfs,mongo,mongodb,pymongo
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Database
Classifier: Typing :: Typed
Requires-Python: >=3.9
Requires-Dist: dnspython<3.0.0,>=1.16.0
Provides-Extra: aws
Requires-Dist: pymongo-auth-aws<2.0.0,>=1.1.0; extra == 'aws'
Provides-Extra: docs
Requires-Dist: furo==2025.7.19; extra == 'docs'
Requires-Dist: readthedocs-sphinx-search~=0.3; extra == 'docs'
Requires-Dist: sphinx-autobuild>=2020.9.1; extra == 'docs'
Requires-Dist: sphinx-rtd-theme<4,>=2; extra == 'docs'
Requires-Dist: sphinx<9,>=5.3; extra == 'docs'
Requires-Dist: sphinxcontrib-shellcheck<2,>=1; extra == 'docs'
Provides-Extra: encryption
Requires-Dist: certifi; (os_name == 'nt' or sys_platform == 'darwin') and extra == 'encryption'
Requires-Dist: pymongo-auth-aws<2.0.0,>=1.1.0; extra == 'encryption'
Requires-Dist: pymongocrypt<2.0.0,>=1.13.0; extra == 'encryption'
Provides-Extra: gssapi
Requires-Dist: pykerberos; (os_name != 'nt') and extra == 'gssapi'
Requires-Dist: winkerberos>=0.5.0; (os_name == 'nt') and extra == 'gssapi'
Provides-Extra: ocsp
Requires-Dist: certifi; (os_name == 'nt' or sys_platform == 'darwin') and extra == 'ocsp'
Requires-Dist: cryptography>=2.5; extra == 'ocsp'
Requires-Dist: pyopenssl>=17.2.0; extra == 'ocsp'
Requires-Dist: requests<3.0.0; extra == 'ocsp'
Requires-Dist: service-identity>=18.1.0; extra == 'ocsp'
Provides-Extra: snappy
Requires-Dist: python-snappy; extra == 'snappy'
Provides-Extra: test
Requires-Dist: pytest-asyncio>=0.24.0; extra == 'test'
Requires-Dist: pytest>=8.2; extra == 'test'
Provides-Extra: zstd
Requires-Dist: zstandard; extra == 'zstd'
Description-Content-Type: text/markdown

# PyMongo

[![PyPI Version](https://img.shields.io/pypi/v/pymongo)](https://pypi.org/project/pymongo)
[![Python Versions](https://img.shields.io/pypi/pyversions/pymongo)](https://pypi.org/project/pymongo)
[![Monthly Downloads](https://static.pepy.tech/badge/pymongo/month)](https://pepy.tech/project/pymongo)
[![API Documentation Status](https://readthedocs.org/projects/pymongo/badge/?version=stable)](http://pymongo.readthedocs.io/en/stable/api?badge=stable)

## About

The PyMongo distribution contains tools for interacting with MongoDB
database from Python. The `bson` package is an implementation of the
[BSON format](http://bsonspec.org) for Python. The `pymongo` package is
a native Python driver for MongoDB, offering both synchronous and asynchronous APIs. The `gridfs` package is a
[gridfs](https://github.com/mongodb/specifications/blob/master/source/gridfs/gridfs-spec.md/)
implementation on top of `pymongo`.

PyMongo supports MongoDB 4.0, 4.2, 4.4, 5.0, 6.0, 7.0, and 8.0. PyMongo follows [semantic versioning](https://semver.org/spec/v2.0.0.html) for its releases.

## Support / Feedback

For issues with, questions about, or feedback for PyMongo, please look
into our [support channels](https://support.mongodb.com/welcome). Please
do not email any of the PyMongo developers directly with issues or
questions - you're more likely to get an answer on
[StackOverflow](https://stackoverflow.com/questions/tagged/mongodb)
(using a "mongodb" tag).

## Bugs / Feature Requests

Think you've found a bug? Want to see a new feature in PyMongo? Please
open a case in our issue management tool, JIRA:

-   [Create an account and login](https://jira.mongodb.org).
-   Navigate to [the PYTHON
    project](https://jira.mongodb.org/browse/PYTHON).
-   Click **Create Issue** - Please provide as much information as
    possible about the issue type and how to reproduce it.

Bug reports in JIRA for all driver projects (i.e. PYTHON, CSHARP, JAVA)
and the Core Server (i.e. SERVER) project are **public**.

### How To Ask For Help

Please include all of the following information when opening an issue:

-   Detailed steps to reproduce the problem, including full traceback,
    if possible.

-   The exact python version used, with patch level:

```bash
python -c "import sys; print(sys.version)"
```

-   The exact version of PyMongo used, with patch level:

```bash
python -c "import pymongo; print(pymongo.version); print(pymongo.has_c())"
```

-   The operating system and version (e.g. Windows 7, OSX 10.8, ...)

-   Web framework or asynchronous network library used, if any, with
    version (e.g. Django 1.7, mod_wsgi 4.3.0, gevent 1.0.1, Tornado
    4.0.2, ...)

### Security Vulnerabilities

If you've identified a security vulnerability in a driver or any other
MongoDB project, please report it according to the [instructions
here](https://www.mongodb.com/docs/manual/tutorial/create-a-vulnerability-report/).

## Installation

PyMongo can be installed with [pip](http://pypi.python.org/pypi/pip):

```bash
python -m pip install pymongo
```

You can also download the project source and do:

```bash
pip install .
```

Do **not** install the "bson" package from pypi. PyMongo comes with
its own bson package; running "pip install bson" installs a third-party
package that is incompatible with PyMongo.

## Dependencies

PyMongo supports CPython 3.9+ and PyPy3.10+.

Required dependencies:

Support for `mongodb+srv://` URIs requires [dnspython](https://pypi.python.org/pypi/dnspython)

Optional dependencies:

GSSAPI authentication requires
[pykerberos](https://pypi.python.org/pypi/pykerberos) on Unix or
[WinKerberos](https://pypi.python.org/pypi/winkerberos) on Windows. The
correct dependency can be installed automatically along with PyMongo:

```bash
python -m pip install "pymongo[gssapi]"
```

MONGODB-AWS authentication requires
[pymongo-auth-aws](https://pypi.org/project/pymongo-auth-aws/):

```bash
python -m pip install "pymongo[aws]"
```

OCSP (Online Certificate Status Protocol) requires
[PyOpenSSL](https://pypi.org/project/pyOpenSSL/),
[requests](https://pypi.org/project/requests/),
[service_identity](https://pypi.org/project/service_identity/) and may
require [certifi](https://pypi.python.org/pypi/certifi):

```bash
python -m pip install "pymongo[ocsp]"
```

Wire protocol compression with snappy requires
[python-snappy](https://pypi.org/project/python-snappy):

```bash
python -m pip install "pymongo[snappy]"
```

Wire protocol compression with zstandard requires
[zstandard](https://pypi.org/project/zstandard):

```bash
python -m pip install "pymongo[zstd]"
```

Client-Side Field Level Encryption requires
[pymongocrypt](https://pypi.org/project/pymongocrypt/) and
[pymongo-auth-aws](https://pypi.org/project/pymongo-auth-aws/):

```bash
python -m pip install "pymongo[encryption]"
```
You can install all dependencies automatically with the following
command:

```bash
python -m pip install "pymongo[gssapi,aws,ocsp,snappy,zstd,encryption]"
```

## Examples

Here's a basic example (for more see the *examples* section of the
docs):

```pycon
>>> import pymongo
>>> client = pymongo.MongoClient("localhost", 27017)
>>> db = client.test
>>> db.name
'test'
>>> db.my_collection
Collection(Database(MongoClient('localhost', 27017), 'test'), 'my_collection')
>>> db.my_collection.insert_one({"x": 10}).inserted_id
ObjectId('4aba15ebe23f6b53b0000000')
>>> db.my_collection.insert_one({"x": 8}).inserted_id
ObjectId('4aba160ee23f6b543e000000')
>>> db.my_collection.insert_one({"x": 11}).inserted_id
ObjectId('4aba160ee23f6b543e000002')
>>> db.my_collection.find_one()
{'x': 10, '_id': ObjectId('4aba15ebe23f6b53b0000000')}
>>> for item in db.my_collection.find():
...     print(item["x"])
...
10
8
11
>>> db.my_collection.create_index("x")
'x_1'
>>> for item in db.my_collection.find().sort("x", pymongo.ASCENDING):
...     print(item["x"])
...
8
10
11
>>> [item["x"] for item in db.my_collection.find().limit(2).skip(1)]
[8, 11]
```

## Documentation

Documentation is available at
[pymongo.readthedocs.io](https://pymongo.readthedocs.io/en/stable/).

See the [contributing guide](./CONTRIBUTING.md#documentation) for how to build the documentation.

## Learning Resources

- MongoDB Learn - [Python
courses](https://learn.mongodb.com/catalog?labels=%5B%22Language%22%5D&values=%5B%22Python%22%5D).
- [Python Articles on Developer
Center](https://www.mongodb.com/developer/languages/python/).

## Testing

The easiest way to run the tests is to run the following from the repository root.

```bash
pip install -e ".[test]"
pytest
```

For more advanced testing scenarios, see the [contributing guide](./CONTRIBUTING.md#running-tests-locally).
