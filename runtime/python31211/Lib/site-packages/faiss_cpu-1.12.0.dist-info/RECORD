faiss/__init__.py,sha256=plV9RGxOgdOlXkNmeW_4E_qyfCv8ulQfsEbTHfIGekI,13027
faiss/__pycache__/__init__.cpython-312.pyc,,
faiss/__pycache__/array_conversions.cpython-312.pyc,,
faiss/__pycache__/class_wrappers.cpython-312.pyc,,
faiss/__pycache__/extra_wrappers.cpython-312.pyc,,
faiss/__pycache__/gpu_wrappers.cpython-312.pyc,,
faiss/__pycache__/loader.cpython-312.pyc,,
faiss/__pycache__/setup.cpython-312.pyc,,
faiss/__pycache__/swigfaiss.cpython-312.pyc,,
faiss/__pycache__/swigfaiss_avx2.cpython-312.pyc,,
faiss/_swigfaiss.cp312-win_amd64.pyd,sha256=UM8ouzB70hTwZzxpAWUwM3WRygg5BXsAUekxX2yhIcQ,11170816
faiss/_swigfaiss_avx2.cp312-win_amd64.pyd,sha256=FjUY2i6yyN9r_vrcSF5dOVT7ReaOqdk5gAx_a_5Mspo,10718208
faiss/array_conversions.py,sha256=nVKx-YM9ewGZSqLKA9eJbatp8NJWNoPoZhP17UnQE_A,5962
faiss/class_wrappers.py,sha256=MHotewTxiTS78tUmxR3Q4XOECUlgpTVU3n3s9oDSCl8,51116
faiss/contrib/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
faiss/contrib/__pycache__/__init__.cpython-312.pyc,,
faiss/contrib/__pycache__/big_batch_search.cpython-312.pyc,,
faiss/contrib/__pycache__/client_server.cpython-312.pyc,,
faiss/contrib/__pycache__/clustering.cpython-312.pyc,,
faiss/contrib/__pycache__/datasets.cpython-312.pyc,,
faiss/contrib/__pycache__/evaluation.cpython-312.pyc,,
faiss/contrib/__pycache__/exhaustive_search.cpython-312.pyc,,
faiss/contrib/__pycache__/factory_tools.cpython-312.pyc,,
faiss/contrib/__pycache__/inspect_tools.cpython-312.pyc,,
faiss/contrib/__pycache__/ivf_tools.cpython-312.pyc,,
faiss/contrib/__pycache__/ondisk.cpython-312.pyc,,
faiss/contrib/__pycache__/rpc.cpython-312.pyc,,
faiss/contrib/__pycache__/torch_utils.cpython-312.pyc,,
faiss/contrib/__pycache__/vecs_io.cpython-312.pyc,,
faiss/contrib/big_batch_search.py,sha256=VBkUTYtr6tviDh14yOpUyb-lul4mSOqQyCEt16gz7Cs,18155
faiss/contrib/client_server.py,sha256=DjZH447C8vEfqAb8YMpRCJ7QWr1jkwixLdXNQWiR38s,2865
faiss/contrib/clustering.py,sha256=chssE7iEWDkYrOMNr8IVjTyZMuaSgtu0IOvow4BNDqc,13127
faiss/contrib/datasets.py,sha256=82uf5MsXHm_TsrdU5jYQ4NB8GWoCycRnGel5j9svVaU,12660
faiss/contrib/evaluation.py,sha256=v5tt5h1oo3WCcML98rIXx41BAG6lxI4bi-UEQa5HsUQ,15487
faiss/contrib/exhaustive_search.py,sha256=Mj4KVVlxkKb8M4QCOJLlzzabgcjvOCqR0Cw-AGETNZE,12747
faiss/contrib/factory_tools.py,sha256=vdj1uU7OhyRAJlGGOcpT3pczPzTzSwvsbTBHlzwfO0c,5234
faiss/contrib/inspect_tools.py,sha256=1jCGH5E8KUnoUW3d9PhA15g53saq8Hn38mFB_X3IKQo,3866
faiss/contrib/ivf_tools.py,sha256=aeFHfMwtgAf1gV9sNfodblfMC3eA8WUta9KXLQF3JWs,5022
faiss/contrib/ondisk.py,sha256=Oqg8c58L-GvkKzI7-UhGKp-DgPAhB9jEmFBHNOkTIow,2128
faiss/contrib/rpc.py,sha256=7Z3nhtQMe1pgTijm5T4PGXqqt3F-ONercYHidCdp_Rg,7563
faiss/contrib/torch/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
faiss/contrib/torch/__pycache__/__init__.cpython-312.pyc,,
faiss/contrib/torch/__pycache__/clustering.cpython-312.pyc,,
faiss/contrib/torch/__pycache__/quantization.cpython-312.pyc,,
faiss/contrib/torch/clustering.py,sha256=Nmtnyg3-fGKLQ3YpYgSgxA_3cD8n4U_3mR_yvzYFMcU,1682
faiss/contrib/torch/quantization.py,sha256=9Y1usws9z-fP47NOFGRoqKMLzl3gnoOWM9IUbrN9mns,2892
faiss/contrib/torch_utils.py,sha256=389p09wL1JR9nx5mru-9rCJSu-F8CNiot2a5C8AkGdQ,28846
faiss/contrib/vecs_io.py,sha256=mct87U7mZ2DMgl8HUkHZtwRiAhNk4dHLPfwHyKPiK4U,1443
faiss/extra_wrappers.py,sha256=GPqcaqmH4fvSgxO7aexwcPawNhKIMlJcwnWzZgffVHE,21151
faiss/gpu_wrappers.py,sha256=OReZ0N39JFV-xZHPI5whqd9BXcJk9ciyZwe7tanddTA,9489
faiss/loader.py,sha256=9tIWP5fDgpaC0ySmfDpoY9ikmGWS5KvIJxbQD58teNY,6176
faiss/python_callbacks.h,sha256=07S0kar8j3vfs06egrhUIuyPvhSMECx6t_dGZtLpN7Q,2663
faiss/setup.py,sha256=jDFU1h_iHuqchWX6ttbYzo_EghxJg-Rm3TdZ7K7avTE,4993
faiss/swigfaiss.i,sha256=1s6byGDe5FHmhuU_M3IwEf7BKd1fqRNoc5jjBiVC5wA,37590
faiss/swigfaiss.py,sha256=_dHWTy5w6xJOkSRfcu9foVdT5h2lnK_1AseQ82-oe1U,557536
faiss/swigfaiss_avx2.py,sha256=v1HhPMpmRNQ-o3IoCn9UzlTBeqeYd3yn-hoMkhqGgsU,580146
faiss_cpu-1.12.0.dist-info/DELVEWHEEL,sha256=NsDFcyBojjh75NBhQlnPS0OUBBb7A_kB9zXqW4QaXu0,409
faiss_cpu-1.12.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
faiss_cpu-1.12.0.dist-info/METADATA,sha256=YufU91YZd6aNUKiG4T7RylOQrDRPPDKM81kymqcxTHY,5238
faiss_cpu-1.12.0.dist-info/RECORD,,
faiss_cpu-1.12.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
faiss_cpu-1.12.0.dist-info/WHEEL,sha256=8UP9x9puWI0P1V_d7K2oMTBqfeLNm21CTzZ_Ptr0NXU,101
faiss_cpu-1.12.0.dist-info/licenses/LICENSE,sha256=SJxdqdQ2exFo_NAY7cECLSMQJiwecF3aA-fbVYHJTC4,1092
faiss_cpu-1.12.0.dist-info/licenses/NOTICE,sha256=k6RNDJXN4EHcvhjkWhS3aAx28KwJqDoKQgh1DeU05WM,366
faiss_cpu-1.12.0.dist-info/top_level.txt,sha256=nr2S-1YAhqAuAlFJukntDvRGXfI1KmwnUNs_iFlNiig,6
faiss_cpu.libs/libopenblas-e824cf9fc22e5949807ce995a32e413a.dll,sha256=6CTPn8IuWUmAfOmVoy5BOjRfuX5j5tEM-_QaqGOCGTw,51076488
faiss_cpu.libs/msvcp140-a4c2229bdc2a2a630acdc095b4d86008.dll,sha256=pMIim9wqKmMKzcCVtNhgCOXD47x3cxdDVPPaT1vrnN4,575056
faiss_cpu.libs/vcomp140-55aba23cdcd6484fbb06f4155b8ca75a.dll,sha256=VauiPNzWSE-7BvQVW4ynWt_OeogfEK_QxJRXFl5ncWQ,193152
