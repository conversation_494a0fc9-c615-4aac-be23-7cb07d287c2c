Metadata-Version: 2.3
Name: strawberry-graphql
Version: 0.274.2
Summary: A library for creating GraphQL APIs
License: MIT
Keywords: graphql,api,rest,starlette,async
Author: <PERSON>
Author-email: <EMAIL>
Requires-Python: >=3.9,<4.0
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Libraries
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: License :: OSI Approved :: MIT License
Provides-Extra: aiohttp
Provides-Extra: asgi
Provides-Extra: chalice
Provides-Extra: channels
Provides-Extra: cli
Provides-Extra: debug
Provides-Extra: debug-server
Provides-Extra: django
Provides-Extra: fastapi
Provides-Extra: flask
Provides-Extra: litestar
Provides-Extra: opentelemetry
Provides-Extra: pydantic
Provides-Extra: pyinstrument
Provides-Extra: quart
Provides-Extra: sanic
Requires-Dist: Django (>=3.2) ; extra == "django"
Requires-Dist: aiohttp (>=3.7.4.post0,<4) ; extra == "aiohttp"
Requires-Dist: asgiref (>=3.2,<4.0) ; extra == "channels"
Requires-Dist: asgiref (>=3.2,<4.0) ; extra == "django"
Requires-Dist: chalice (>=1.22,<2.0) ; extra == "chalice"
Requires-Dist: channels (>=3.0.5) ; extra == "channels"
Requires-Dist: fastapi (>=0.65.2) ; extra == "fastapi"
Requires-Dist: flask (>=1.1) ; extra == "flask"
Requires-Dist: graphql-core (>=3.2.0,<3.4.0)
Requires-Dist: libcst (>=0.4.7,<1.8.0) ; extra == "cli"
Requires-Dist: libcst (>=0.4.7,<1.8.0) ; extra == "debug"
Requires-Dist: libcst (>=0.4.7,<1.8.0) ; extra == "debug-server"
Requires-Dist: litestar (>=2) ; (python_version ~= "3.10") and (extra == "litestar")
Requires-Dist: opentelemetry-api (<2) ; extra == "opentelemetry"
Requires-Dist: opentelemetry-sdk (<2) ; extra == "opentelemetry"
Requires-Dist: packaging (>=23)
Requires-Dist: pydantic (>1.6.1) ; extra == "pydantic"
Requires-Dist: pygments (>=2.3,<3.0) ; extra == "cli"
Requires-Dist: pygments (>=2.3,<3.0) ; extra == "debug-server"
Requires-Dist: pyinstrument (>=4.0.0) ; extra == "pyinstrument"
Requires-Dist: python-dateutil (>=2.7,<3.0)
Requires-Dist: python-multipart (>=0.0.7) ; extra == "asgi"
Requires-Dist: python-multipart (>=0.0.7) ; extra == "debug-server"
Requires-Dist: python-multipart (>=0.0.7) ; extra == "fastapi"
Requires-Dist: quart (>=0.19.3) ; extra == "quart"
Requires-Dist: rich (>=12.0.0) ; extra == "cli"
Requires-Dist: rich (>=12.0.0) ; extra == "debug"
Requires-Dist: rich (>=12.0.0) ; extra == "debug-server"
Requires-Dist: sanic (>=20.12.2) ; extra == "sanic"
Requires-Dist: starlette (>=0.18.0) ; extra == "asgi"
Requires-Dist: starlette (>=0.18.0) ; extra == "debug-server"
Requires-Dist: typer (>=0.7.0) ; extra == "cli"
Requires-Dist: typer (>=0.7.0) ; extra == "debug-server"
Requires-Dist: typing-extensions (>=4.5.0)
Requires-Dist: uvicorn (>=0.11.6) ; extra == "debug-server"
Requires-Dist: websockets (>=15.0.1,<16) ; extra == "debug-server"
Project-URL: Changelog, https://strawberry.rocks/changelog
Project-URL: Documentation, https://strawberry.rocks/
Project-URL: Discord, https://discord.com/invite/3uQ2PaY
Project-URL: Homepage, https://strawberry.rocks/
Project-URL: Mastodon, https://farbun.social/@strawberry
Project-URL: Repository, https://github.com/strawberry-graphql/strawberry
Project-URL: Sponsor on GitHub, https://github.com/sponsors/strawberry-graphql
Project-URL: Sponsor on Open Collective, https://opencollective.com/strawberry-graphql
Project-URL: Twitter, https://twitter.com/strawberry_gql
Description-Content-Type: text/markdown

<img src="https://github.com/strawberry-graphql/strawberry/raw/main/.github/logo.png" width="124" height="150">

# Strawberry GraphQL

> Python GraphQL library based on dataclasses

[![CircleCI](https://img.shields.io/circleci/token/****************************************/project/github/strawberry-graphql/strawberry/main.svg?style=for-the-badge)](https://circleci.com/gh/strawberry-graphql/strawberry/tree/main)
[![Discord](https://img.shields.io/discord/689806334337482765?label=discord&logo=discord&logoColor=white&style=for-the-badge&color=blue)](https://discord.gg/ZkRTEJQ)
[![PyPI](https://img.shields.io/pypi/v/strawberry-graphql?logo=pypi&logoColor=white&style=for-the-badge)](https://pypi.org/project/strawberry-graphql/)

## Installation ( Quick Start )

The quick start method provides a server and CLI to get going quickly. Install
with:

```shell
pip install "strawberry-graphql[debug-server]"
```

## Getting Started

Create a file called `app.py` with the following code:

```python
import strawberry


@strawberry.type
class User:
    name: str
    age: int


@strawberry.type
class Query:
    @strawberry.field
    def user(self) -> User:
        return User(name="Patrick", age=100)


schema = strawberry.Schema(query=Query)
```

This will create a GraphQL schema defining a `User` type and a single query
field `user` that will return a hardcoded user.

To run the debug server run the following command:

```shell
strawberry server app
```

Open the debug server by clicking on the following link:
[http://0.0.0.0:8000/graphql](http://0.0.0.0:8000/graphql)

This will open GraphiQL where you can test the API.

### Type-checking

Strawberry comes with a [mypy] plugin that enables statically type-checking your
GraphQL schema. To enable it, add the following lines to your `mypy.ini`
configuration:

```ini
[mypy]
plugins = strawberry.ext.mypy_plugin
```

[mypy]: http://www.mypy-lang.org/

### Django Integration

A Django view is provided for adding a GraphQL endpoint to your application.

1. Add the app to your `INSTALLED_APPS`.

```python
INSTALLED_APPS = [
    ...,  # your other apps
    "strawberry.django",
]
```

2. Add the view to your `urls.py` file.

```python
from strawberry.django.views import GraphQLView
from .schema import schema

urlpatterns = [
    ...,
    path("graphql", GraphQLView.as_view(schema=schema)),
]
```

## Examples

* [Various examples on how to use Strawberry](https://github.com/strawberry-graphql/examples)
* [Full stack example using Starlette, SQLAlchemy, Typescript codegen and Next.js](https://github.com/jokull/python-ts-graphql-demo)
* [Quart + Strawberry tutorial](https://github.com/rockyburt/Ketchup)

## Contributing

We use [poetry](https://github.com/sdispater/poetry) to manage dependencies, to
get started follow these steps:

```shell
git clone https://github.com/strawberry-graphql/strawberry
cd strawberry
poetry install --with integrations
poetry run pytest
```

For all further detail, check out the [Contributing Page](CONTRIBUTING.md)


### Pre commit

We have a configuration for
[pre-commit](https://github.com/pre-commit/pre-commit), to add the hook run the
following command:

```shell
pre-commit install
```

## Links

- Project homepage: https://strawberry.rocks
- Repository: https://github.com/strawberry-graphql/strawberry
- Issue tracker: https://github.com/strawberry-graphql/strawberry/issues
  - In case of sensitive bugs like security vulnerabilities, please contact
    <EMAIL> directly instead of using the issue tracker. We
    value your effort to improve the security and privacy of this project!

## Licensing

The code in this project is licensed under MIT license. See [LICENSE](./LICENSE)
for more information.

![Recent Activity](https://images.repography.com/0/strawberry-graphql/strawberry/recent-activity/d751713988987e9331980363e24189ce.svg)

