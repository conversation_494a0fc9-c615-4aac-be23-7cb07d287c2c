#ifndef Py_INTERNAL_RUNTIME_INIT_GENERATED_H
#define Py_INTERNAL_RUNTIME_INIT_GENERATED_H
#ifdef __cplusplus
extern "C" {
#endif

#ifndef Py_BUILD_CORE
#  error "this header requires Py_BUILD_CORE define"
#endif

/* The following is auto-generated by Tools/build/generate_global_objects.py. */
#define _Py_small_ints_INIT { \
    _PyLong_DIGIT_INIT(-5), \
    _PyLong_DIGIT_INIT(-4), \
    _PyLong_DIGIT_INIT(-3), \
    _PyLong_DIGIT_INIT(-2), \
    _PyLong_DIGIT_INIT(-1), \
    _PyLong_DIGIT_INIT(0), \
    _PyLong_DIGIT_INIT(1), \
    _PyLong_DIGIT_INIT(2), \
    _PyLong_DIGIT_INIT(3), \
    _PyLong_DIGIT_INIT(4), \
    _PyLong_DIGIT_INIT(5), \
    _PyLong_DIGIT_INIT(6), \
    _PyLong_DIGIT_INIT(7), \
    _PyLong_DIGIT_INIT(8), \
    _PyLong_DIGIT_INIT(9), \
    _PyLong_DIGIT_INIT(10), \
    _PyLong_DIGIT_INIT(11), \
    _PyLong_DIGIT_INIT(12), \
    _PyLong_DIGIT_INIT(13), \
    _PyLong_DIGIT_INIT(14), \
    _PyLong_DIGIT_INIT(15), \
    _PyLong_DIGIT_INIT(16), \
    _PyLong_DIGIT_INIT(17), \
    _PyLong_DIGIT_INIT(18), \
    _PyLong_DIGIT_INIT(19), \
    _PyLong_DIGIT_INIT(20), \
    _PyLong_DIGIT_INIT(21), \
    _PyLong_DIGIT_INIT(22), \
    _PyLong_DIGIT_INIT(23), \
    _PyLong_DIGIT_INIT(24), \
    _PyLong_DIGIT_INIT(25), \
    _PyLong_DIGIT_INIT(26), \
    _PyLong_DIGIT_INIT(27), \
    _PyLong_DIGIT_INIT(28), \
    _PyLong_DIGIT_INIT(29), \
    _PyLong_DIGIT_INIT(30), \
    _PyLong_DIGIT_INIT(31), \
    _PyLong_DIGIT_INIT(32), \
    _PyLong_DIGIT_INIT(33), \
    _PyLong_DIGIT_INIT(34), \
    _PyLong_DIGIT_INIT(35), \
    _PyLong_DIGIT_INIT(36), \
    _PyLong_DIGIT_INIT(37), \
    _PyLong_DIGIT_INIT(38), \
    _PyLong_DIGIT_INIT(39), \
    _PyLong_DIGIT_INIT(40), \
    _PyLong_DIGIT_INIT(41), \
    _PyLong_DIGIT_INIT(42), \
    _PyLong_DIGIT_INIT(43), \
    _PyLong_DIGIT_INIT(44), \
    _PyLong_DIGIT_INIT(45), \
    _PyLong_DIGIT_INIT(46), \
    _PyLong_DIGIT_INIT(47), \
    _PyLong_DIGIT_INIT(48), \
    _PyLong_DIGIT_INIT(49), \
    _PyLong_DIGIT_INIT(50), \
    _PyLong_DIGIT_INIT(51), \
    _PyLong_DIGIT_INIT(52), \
    _PyLong_DIGIT_INIT(53), \
    _PyLong_DIGIT_INIT(54), \
    _PyLong_DIGIT_INIT(55), \
    _PyLong_DIGIT_INIT(56), \
    _PyLong_DIGIT_INIT(57), \
    _PyLong_DIGIT_INIT(58), \
    _PyLong_DIGIT_INIT(59), \
    _PyLong_DIGIT_INIT(60), \
    _PyLong_DIGIT_INIT(61), \
    _PyLong_DIGIT_INIT(62), \
    _PyLong_DIGIT_INIT(63), \
    _PyLong_DIGIT_INIT(64), \
    _PyLong_DIGIT_INIT(65), \
    _PyLong_DIGIT_INIT(66), \
    _PyLong_DIGIT_INIT(67), \
    _PyLong_DIGIT_INIT(68), \
    _PyLong_DIGIT_INIT(69), \
    _PyLong_DIGIT_INIT(70), \
    _PyLong_DIGIT_INIT(71), \
    _PyLong_DIGIT_INIT(72), \
    _PyLong_DIGIT_INIT(73), \
    _PyLong_DIGIT_INIT(74), \
    _PyLong_DIGIT_INIT(75), \
    _PyLong_DIGIT_INIT(76), \
    _PyLong_DIGIT_INIT(77), \
    _PyLong_DIGIT_INIT(78), \
    _PyLong_DIGIT_INIT(79), \
    _PyLong_DIGIT_INIT(80), \
    _PyLong_DIGIT_INIT(81), \
    _PyLong_DIGIT_INIT(82), \
    _PyLong_DIGIT_INIT(83), \
    _PyLong_DIGIT_INIT(84), \
    _PyLong_DIGIT_INIT(85), \
    _PyLong_DIGIT_INIT(86), \
    _PyLong_DIGIT_INIT(87), \
    _PyLong_DIGIT_INIT(88), \
    _PyLong_DIGIT_INIT(89), \
    _PyLong_DIGIT_INIT(90), \
    _PyLong_DIGIT_INIT(91), \
    _PyLong_DIGIT_INIT(92), \
    _PyLong_DIGIT_INIT(93), \
    _PyLong_DIGIT_INIT(94), \
    _PyLong_DIGIT_INIT(95), \
    _PyLong_DIGIT_INIT(96), \
    _PyLong_DIGIT_INIT(97), \
    _PyLong_DIGIT_INIT(98), \
    _PyLong_DIGIT_INIT(99), \
    _PyLong_DIGIT_INIT(100), \
    _PyLong_DIGIT_INIT(101), \
    _PyLong_DIGIT_INIT(102), \
    _PyLong_DIGIT_INIT(103), \
    _PyLong_DIGIT_INIT(104), \
    _PyLong_DIGIT_INIT(105), \
    _PyLong_DIGIT_INIT(106), \
    _PyLong_DIGIT_INIT(107), \
    _PyLong_DIGIT_INIT(108), \
    _PyLong_DIGIT_INIT(109), \
    _PyLong_DIGIT_INIT(110), \
    _PyLong_DIGIT_INIT(111), \
    _PyLong_DIGIT_INIT(112), \
    _PyLong_DIGIT_INIT(113), \
    _PyLong_DIGIT_INIT(114), \
    _PyLong_DIGIT_INIT(115), \
    _PyLong_DIGIT_INIT(116), \
    _PyLong_DIGIT_INIT(117), \
    _PyLong_DIGIT_INIT(118), \
    _PyLong_DIGIT_INIT(119), \
    _PyLong_DIGIT_INIT(120), \
    _PyLong_DIGIT_INIT(121), \
    _PyLong_DIGIT_INIT(122), \
    _PyLong_DIGIT_INIT(123), \
    _PyLong_DIGIT_INIT(124), \
    _PyLong_DIGIT_INIT(125), \
    _PyLong_DIGIT_INIT(126), \
    _PyLong_DIGIT_INIT(127), \
    _PyLong_DIGIT_INIT(128), \
    _PyLong_DIGIT_INIT(129), \
    _PyLong_DIGIT_INIT(130), \
    _PyLong_DIGIT_INIT(131), \
    _PyLong_DIGIT_INIT(132), \
    _PyLong_DIGIT_INIT(133), \
    _PyLong_DIGIT_INIT(134), \
    _PyLong_DIGIT_INIT(135), \
    _PyLong_DIGIT_INIT(136), \
    _PyLong_DIGIT_INIT(137), \
    _PyLong_DIGIT_INIT(138), \
    _PyLong_DIGIT_INIT(139), \
    _PyLong_DIGIT_INIT(140), \
    _PyLong_DIGIT_INIT(141), \
    _PyLong_DIGIT_INIT(142), \
    _PyLong_DIGIT_INIT(143), \
    _PyLong_DIGIT_INIT(144), \
    _PyLong_DIGIT_INIT(145), \
    _PyLong_DIGIT_INIT(146), \
    _PyLong_DIGIT_INIT(147), \
    _PyLong_DIGIT_INIT(148), \
    _PyLong_DIGIT_INIT(149), \
    _PyLong_DIGIT_INIT(150), \
    _PyLong_DIGIT_INIT(151), \
    _PyLong_DIGIT_INIT(152), \
    _PyLong_DIGIT_INIT(153), \
    _PyLong_DIGIT_INIT(154), \
    _PyLong_DIGIT_INIT(155), \
    _PyLong_DIGIT_INIT(156), \
    _PyLong_DIGIT_INIT(157), \
    _PyLong_DIGIT_INIT(158), \
    _PyLong_DIGIT_INIT(159), \
    _PyLong_DIGIT_INIT(160), \
    _PyLong_DIGIT_INIT(161), \
    _PyLong_DIGIT_INIT(162), \
    _PyLong_DIGIT_INIT(163), \
    _PyLong_DIGIT_INIT(164), \
    _PyLong_DIGIT_INIT(165), \
    _PyLong_DIGIT_INIT(166), \
    _PyLong_DIGIT_INIT(167), \
    _PyLong_DIGIT_INIT(168), \
    _PyLong_DIGIT_INIT(169), \
    _PyLong_DIGIT_INIT(170), \
    _PyLong_DIGIT_INIT(171), \
    _PyLong_DIGIT_INIT(172), \
    _PyLong_DIGIT_INIT(173), \
    _PyLong_DIGIT_INIT(174), \
    _PyLong_DIGIT_INIT(175), \
    _PyLong_DIGIT_INIT(176), \
    _PyLong_DIGIT_INIT(177), \
    _PyLong_DIGIT_INIT(178), \
    _PyLong_DIGIT_INIT(179), \
    _PyLong_DIGIT_INIT(180), \
    _PyLong_DIGIT_INIT(181), \
    _PyLong_DIGIT_INIT(182), \
    _PyLong_DIGIT_INIT(183), \
    _PyLong_DIGIT_INIT(184), \
    _PyLong_DIGIT_INIT(185), \
    _PyLong_DIGIT_INIT(186), \
    _PyLong_DIGIT_INIT(187), \
    _PyLong_DIGIT_INIT(188), \
    _PyLong_DIGIT_INIT(189), \
    _PyLong_DIGIT_INIT(190), \
    _PyLong_DIGIT_INIT(191), \
    _PyLong_DIGIT_INIT(192), \
    _PyLong_DIGIT_INIT(193), \
    _PyLong_DIGIT_INIT(194), \
    _PyLong_DIGIT_INIT(195), \
    _PyLong_DIGIT_INIT(196), \
    _PyLong_DIGIT_INIT(197), \
    _PyLong_DIGIT_INIT(198), \
    _PyLong_DIGIT_INIT(199), \
    _PyLong_DIGIT_INIT(200), \
    _PyLong_DIGIT_INIT(201), \
    _PyLong_DIGIT_INIT(202), \
    _PyLong_DIGIT_INIT(203), \
    _PyLong_DIGIT_INIT(204), \
    _PyLong_DIGIT_INIT(205), \
    _PyLong_DIGIT_INIT(206), \
    _PyLong_DIGIT_INIT(207), \
    _PyLong_DIGIT_INIT(208), \
    _PyLong_DIGIT_INIT(209), \
    _PyLong_DIGIT_INIT(210), \
    _PyLong_DIGIT_INIT(211), \
    _PyLong_DIGIT_INIT(212), \
    _PyLong_DIGIT_INIT(213), \
    _PyLong_DIGIT_INIT(214), \
    _PyLong_DIGIT_INIT(215), \
    _PyLong_DIGIT_INIT(216), \
    _PyLong_DIGIT_INIT(217), \
    _PyLong_DIGIT_INIT(218), \
    _PyLong_DIGIT_INIT(219), \
    _PyLong_DIGIT_INIT(220), \
    _PyLong_DIGIT_INIT(221), \
    _PyLong_DIGIT_INIT(222), \
    _PyLong_DIGIT_INIT(223), \
    _PyLong_DIGIT_INIT(224), \
    _PyLong_DIGIT_INIT(225), \
    _PyLong_DIGIT_INIT(226), \
    _PyLong_DIGIT_INIT(227), \
    _PyLong_DIGIT_INIT(228), \
    _PyLong_DIGIT_INIT(229), \
    _PyLong_DIGIT_INIT(230), \
    _PyLong_DIGIT_INIT(231), \
    _PyLong_DIGIT_INIT(232), \
    _PyLong_DIGIT_INIT(233), \
    _PyLong_DIGIT_INIT(234), \
    _PyLong_DIGIT_INIT(235), \
    _PyLong_DIGIT_INIT(236), \
    _PyLong_DIGIT_INIT(237), \
    _PyLong_DIGIT_INIT(238), \
    _PyLong_DIGIT_INIT(239), \
    _PyLong_DIGIT_INIT(240), \
    _PyLong_DIGIT_INIT(241), \
    _PyLong_DIGIT_INIT(242), \
    _PyLong_DIGIT_INIT(243), \
    _PyLong_DIGIT_INIT(244), \
    _PyLong_DIGIT_INIT(245), \
    _PyLong_DIGIT_INIT(246), \
    _PyLong_DIGIT_INIT(247), \
    _PyLong_DIGIT_INIT(248), \
    _PyLong_DIGIT_INIT(249), \
    _PyLong_DIGIT_INIT(250), \
    _PyLong_DIGIT_INIT(251), \
    _PyLong_DIGIT_INIT(252), \
    _PyLong_DIGIT_INIT(253), \
    _PyLong_DIGIT_INIT(254), \
    _PyLong_DIGIT_INIT(255), \
    _PyLong_DIGIT_INIT(256), \
}

#define _Py_bytes_characters_INIT { \
    _PyBytes_CHAR_INIT(0), \
    _PyBytes_CHAR_INIT(1), \
    _PyBytes_CHAR_INIT(2), \
    _PyBytes_CHAR_INIT(3), \
    _PyBytes_CHAR_INIT(4), \
    _PyBytes_CHAR_INIT(5), \
    _PyBytes_CHAR_INIT(6), \
    _PyBytes_CHAR_INIT(7), \
    _PyBytes_CHAR_INIT(8), \
    _PyBytes_CHAR_INIT(9), \
    _PyBytes_CHAR_INIT(10), \
    _PyBytes_CHAR_INIT(11), \
    _PyBytes_CHAR_INIT(12), \
    _PyBytes_CHAR_INIT(13), \
    _PyBytes_CHAR_INIT(14), \
    _PyBytes_CHAR_INIT(15), \
    _PyBytes_CHAR_INIT(16), \
    _PyBytes_CHAR_INIT(17), \
    _PyBytes_CHAR_INIT(18), \
    _PyBytes_CHAR_INIT(19), \
    _PyBytes_CHAR_INIT(20), \
    _PyBytes_CHAR_INIT(21), \
    _PyBytes_CHAR_INIT(22), \
    _PyBytes_CHAR_INIT(23), \
    _PyBytes_CHAR_INIT(24), \
    _PyBytes_CHAR_INIT(25), \
    _PyBytes_CHAR_INIT(26), \
    _PyBytes_CHAR_INIT(27), \
    _PyBytes_CHAR_INIT(28), \
    _PyBytes_CHAR_INIT(29), \
    _PyBytes_CHAR_INIT(30), \
    _PyBytes_CHAR_INIT(31), \
    _PyBytes_CHAR_INIT(32), \
    _PyBytes_CHAR_INIT(33), \
    _PyBytes_CHAR_INIT(34), \
    _PyBytes_CHAR_INIT(35), \
    _PyBytes_CHAR_INIT(36), \
    _PyBytes_CHAR_INIT(37), \
    _PyBytes_CHAR_INIT(38), \
    _PyBytes_CHAR_INIT(39), \
    _PyBytes_CHAR_INIT(40), \
    _PyBytes_CHAR_INIT(41), \
    _PyBytes_CHAR_INIT(42), \
    _PyBytes_CHAR_INIT(43), \
    _PyBytes_CHAR_INIT(44), \
    _PyBytes_CHAR_INIT(45), \
    _PyBytes_CHAR_INIT(46), \
    _PyBytes_CHAR_INIT(47), \
    _PyBytes_CHAR_INIT(48), \
    _PyBytes_CHAR_INIT(49), \
    _PyBytes_CHAR_INIT(50), \
    _PyBytes_CHAR_INIT(51), \
    _PyBytes_CHAR_INIT(52), \
    _PyBytes_CHAR_INIT(53), \
    _PyBytes_CHAR_INIT(54), \
    _PyBytes_CHAR_INIT(55), \
    _PyBytes_CHAR_INIT(56), \
    _PyBytes_CHAR_INIT(57), \
    _PyBytes_CHAR_INIT(58), \
    _PyBytes_CHAR_INIT(59), \
    _PyBytes_CHAR_INIT(60), \
    _PyBytes_CHAR_INIT(61), \
    _PyBytes_CHAR_INIT(62), \
    _PyBytes_CHAR_INIT(63), \
    _PyBytes_CHAR_INIT(64), \
    _PyBytes_CHAR_INIT(65), \
    _PyBytes_CHAR_INIT(66), \
    _PyBytes_CHAR_INIT(67), \
    _PyBytes_CHAR_INIT(68), \
    _PyBytes_CHAR_INIT(69), \
    _PyBytes_CHAR_INIT(70), \
    _PyBytes_CHAR_INIT(71), \
    _PyBytes_CHAR_INIT(72), \
    _PyBytes_CHAR_INIT(73), \
    _PyBytes_CHAR_INIT(74), \
    _PyBytes_CHAR_INIT(75), \
    _PyBytes_CHAR_INIT(76), \
    _PyBytes_CHAR_INIT(77), \
    _PyBytes_CHAR_INIT(78), \
    _PyBytes_CHAR_INIT(79), \
    _PyBytes_CHAR_INIT(80), \
    _PyBytes_CHAR_INIT(81), \
    _PyBytes_CHAR_INIT(82), \
    _PyBytes_CHAR_INIT(83), \
    _PyBytes_CHAR_INIT(84), \
    _PyBytes_CHAR_INIT(85), \
    _PyBytes_CHAR_INIT(86), \
    _PyBytes_CHAR_INIT(87), \
    _PyBytes_CHAR_INIT(88), \
    _PyBytes_CHAR_INIT(89), \
    _PyBytes_CHAR_INIT(90), \
    _PyBytes_CHAR_INIT(91), \
    _PyBytes_CHAR_INIT(92), \
    _PyBytes_CHAR_INIT(93), \
    _PyBytes_CHAR_INIT(94), \
    _PyBytes_CHAR_INIT(95), \
    _PyBytes_CHAR_INIT(96), \
    _PyBytes_CHAR_INIT(97), \
    _PyBytes_CHAR_INIT(98), \
    _PyBytes_CHAR_INIT(99), \
    _PyBytes_CHAR_INIT(100), \
    _PyBytes_CHAR_INIT(101), \
    _PyBytes_CHAR_INIT(102), \
    _PyBytes_CHAR_INIT(103), \
    _PyBytes_CHAR_INIT(104), \
    _PyBytes_CHAR_INIT(105), \
    _PyBytes_CHAR_INIT(106), \
    _PyBytes_CHAR_INIT(107), \
    _PyBytes_CHAR_INIT(108), \
    _PyBytes_CHAR_INIT(109), \
    _PyBytes_CHAR_INIT(110), \
    _PyBytes_CHAR_INIT(111), \
    _PyBytes_CHAR_INIT(112), \
    _PyBytes_CHAR_INIT(113), \
    _PyBytes_CHAR_INIT(114), \
    _PyBytes_CHAR_INIT(115), \
    _PyBytes_CHAR_INIT(116), \
    _PyBytes_CHAR_INIT(117), \
    _PyBytes_CHAR_INIT(118), \
    _PyBytes_CHAR_INIT(119), \
    _PyBytes_CHAR_INIT(120), \
    _PyBytes_CHAR_INIT(121), \
    _PyBytes_CHAR_INIT(122), \
    _PyBytes_CHAR_INIT(123), \
    _PyBytes_CHAR_INIT(124), \
    _PyBytes_CHAR_INIT(125), \
    _PyBytes_CHAR_INIT(126), \
    _PyBytes_CHAR_INIT(127), \
    _PyBytes_CHAR_INIT(128), \
    _PyBytes_CHAR_INIT(129), \
    _PyBytes_CHAR_INIT(130), \
    _PyBytes_CHAR_INIT(131), \
    _PyBytes_CHAR_INIT(132), \
    _PyBytes_CHAR_INIT(133), \
    _PyBytes_CHAR_INIT(134), \
    _PyBytes_CHAR_INIT(135), \
    _PyBytes_CHAR_INIT(136), \
    _PyBytes_CHAR_INIT(137), \
    _PyBytes_CHAR_INIT(138), \
    _PyBytes_CHAR_INIT(139), \
    _PyBytes_CHAR_INIT(140), \
    _PyBytes_CHAR_INIT(141), \
    _PyBytes_CHAR_INIT(142), \
    _PyBytes_CHAR_INIT(143), \
    _PyBytes_CHAR_INIT(144), \
    _PyBytes_CHAR_INIT(145), \
    _PyBytes_CHAR_INIT(146), \
    _PyBytes_CHAR_INIT(147), \
    _PyBytes_CHAR_INIT(148), \
    _PyBytes_CHAR_INIT(149), \
    _PyBytes_CHAR_INIT(150), \
    _PyBytes_CHAR_INIT(151), \
    _PyBytes_CHAR_INIT(152), \
    _PyBytes_CHAR_INIT(153), \
    _PyBytes_CHAR_INIT(154), \
    _PyBytes_CHAR_INIT(155), \
    _PyBytes_CHAR_INIT(156), \
    _PyBytes_CHAR_INIT(157), \
    _PyBytes_CHAR_INIT(158), \
    _PyBytes_CHAR_INIT(159), \
    _PyBytes_CHAR_INIT(160), \
    _PyBytes_CHAR_INIT(161), \
    _PyBytes_CHAR_INIT(162), \
    _PyBytes_CHAR_INIT(163), \
    _PyBytes_CHAR_INIT(164), \
    _PyBytes_CHAR_INIT(165), \
    _PyBytes_CHAR_INIT(166), \
    _PyBytes_CHAR_INIT(167), \
    _PyBytes_CHAR_INIT(168), \
    _PyBytes_CHAR_INIT(169), \
    _PyBytes_CHAR_INIT(170), \
    _PyBytes_CHAR_INIT(171), \
    _PyBytes_CHAR_INIT(172), \
    _PyBytes_CHAR_INIT(173), \
    _PyBytes_CHAR_INIT(174), \
    _PyBytes_CHAR_INIT(175), \
    _PyBytes_CHAR_INIT(176), \
    _PyBytes_CHAR_INIT(177), \
    _PyBytes_CHAR_INIT(178), \
    _PyBytes_CHAR_INIT(179), \
    _PyBytes_CHAR_INIT(180), \
    _PyBytes_CHAR_INIT(181), \
    _PyBytes_CHAR_INIT(182), \
    _PyBytes_CHAR_INIT(183), \
    _PyBytes_CHAR_INIT(184), \
    _PyBytes_CHAR_INIT(185), \
    _PyBytes_CHAR_INIT(186), \
    _PyBytes_CHAR_INIT(187), \
    _PyBytes_CHAR_INIT(188), \
    _PyBytes_CHAR_INIT(189), \
    _PyBytes_CHAR_INIT(190), \
    _PyBytes_CHAR_INIT(191), \
    _PyBytes_CHAR_INIT(192), \
    _PyBytes_CHAR_INIT(193), \
    _PyBytes_CHAR_INIT(194), \
    _PyBytes_CHAR_INIT(195), \
    _PyBytes_CHAR_INIT(196), \
    _PyBytes_CHAR_INIT(197), \
    _PyBytes_CHAR_INIT(198), \
    _PyBytes_CHAR_INIT(199), \
    _PyBytes_CHAR_INIT(200), \
    _PyBytes_CHAR_INIT(201), \
    _PyBytes_CHAR_INIT(202), \
    _PyBytes_CHAR_INIT(203), \
    _PyBytes_CHAR_INIT(204), \
    _PyBytes_CHAR_INIT(205), \
    _PyBytes_CHAR_INIT(206), \
    _PyBytes_CHAR_INIT(207), \
    _PyBytes_CHAR_INIT(208), \
    _PyBytes_CHAR_INIT(209), \
    _PyBytes_CHAR_INIT(210), \
    _PyBytes_CHAR_INIT(211), \
    _PyBytes_CHAR_INIT(212), \
    _PyBytes_CHAR_INIT(213), \
    _PyBytes_CHAR_INIT(214), \
    _PyBytes_CHAR_INIT(215), \
    _PyBytes_CHAR_INIT(216), \
    _PyBytes_CHAR_INIT(217), \
    _PyBytes_CHAR_INIT(218), \
    _PyBytes_CHAR_INIT(219), \
    _PyBytes_CHAR_INIT(220), \
    _PyBytes_CHAR_INIT(221), \
    _PyBytes_CHAR_INIT(222), \
    _PyBytes_CHAR_INIT(223), \
    _PyBytes_CHAR_INIT(224), \
    _PyBytes_CHAR_INIT(225), \
    _PyBytes_CHAR_INIT(226), \
    _PyBytes_CHAR_INIT(227), \
    _PyBytes_CHAR_INIT(228), \
    _PyBytes_CHAR_INIT(229), \
    _PyBytes_CHAR_INIT(230), \
    _PyBytes_CHAR_INIT(231), \
    _PyBytes_CHAR_INIT(232), \
    _PyBytes_CHAR_INIT(233), \
    _PyBytes_CHAR_INIT(234), \
    _PyBytes_CHAR_INIT(235), \
    _PyBytes_CHAR_INIT(236), \
    _PyBytes_CHAR_INIT(237), \
    _PyBytes_CHAR_INIT(238), \
    _PyBytes_CHAR_INIT(239), \
    _PyBytes_CHAR_INIT(240), \
    _PyBytes_CHAR_INIT(241), \
    _PyBytes_CHAR_INIT(242), \
    _PyBytes_CHAR_INIT(243), \
    _PyBytes_CHAR_INIT(244), \
    _PyBytes_CHAR_INIT(245), \
    _PyBytes_CHAR_INIT(246), \
    _PyBytes_CHAR_INIT(247), \
    _PyBytes_CHAR_INIT(248), \
    _PyBytes_CHAR_INIT(249), \
    _PyBytes_CHAR_INIT(250), \
    _PyBytes_CHAR_INIT(251), \
    _PyBytes_CHAR_INIT(252), \
    _PyBytes_CHAR_INIT(253), \
    _PyBytes_CHAR_INIT(254), \
    _PyBytes_CHAR_INIT(255), \
}

#define _Py_str_literals_INIT { \
    INIT_STR(anon_dictcomp, "<dictcomp>"), \
    INIT_STR(anon_genexpr, "<genexpr>"), \
    INIT_STR(anon_lambda, "<lambda>"), \
    INIT_STR(anon_listcomp, "<listcomp>"), \
    INIT_STR(anon_module, "<module>"), \
    INIT_STR(anon_setcomp, "<setcomp>"), \
    INIT_STR(anon_string, "<string>"), \
    INIT_STR(anon_unknown, "<unknown>"), \
    INIT_STR(dbl_close_br, "}}"), \
    INIT_STR(dbl_open_br, "{{"), \
    INIT_STR(dbl_percent, "%%"), \
    INIT_STR(defaults, ".defaults"), \
    INIT_STR(dot_locals, ".<locals>"), \
    INIT_STR(empty, ""), \
    INIT_STR(generic_base, ".generic_base"), \
    INIT_STR(json_decoder, "json.decoder"), \
    INIT_STR(kwdefaults, ".kwdefaults"), \
    INIT_STR(list_err, "list index out of range"), \
    INIT_STR(shim_name, "<shim>"), \
    INIT_STR(type_params, ".type_params"), \
    INIT_STR(utf_8, "utf-8"), \
}

#define _Py_str_identifiers_INIT { \
    INIT_ID(CANCELLED), \
    INIT_ID(FINISHED), \
    INIT_ID(False), \
    INIT_ID(JSONDecodeError), \
    INIT_ID(PENDING), \
    INIT_ID(Py_Repr), \
    INIT_ID(TextIOWrapper), \
    INIT_ID(True), \
    INIT_ID(WarningMessage), \
    INIT_ID(_WindowsConsoleIO), \
    INIT_ID(__IOBase_closed), \
    INIT_ID(__abc_tpflags__), \
    INIT_ID(__abs__), \
    INIT_ID(__abstractmethods__), \
    INIT_ID(__add__), \
    INIT_ID(__aenter__), \
    INIT_ID(__aexit__), \
    INIT_ID(__aiter__), \
    INIT_ID(__all__), \
    INIT_ID(__and__), \
    INIT_ID(__anext__), \
    INIT_ID(__annotations__), \
    INIT_ID(__args__), \
    INIT_ID(__asyncio_running_event_loop__), \
    INIT_ID(__await__), \
    INIT_ID(__bases__), \
    INIT_ID(__bool__), \
    INIT_ID(__buffer__), \
    INIT_ID(__build_class__), \
    INIT_ID(__builtins__), \
    INIT_ID(__bytes__), \
    INIT_ID(__call__), \
    INIT_ID(__cantrace__), \
    INIT_ID(__class__), \
    INIT_ID(__class_getitem__), \
    INIT_ID(__classcell__), \
    INIT_ID(__classdict__), \
    INIT_ID(__classdictcell__), \
    INIT_ID(__complex__), \
    INIT_ID(__contains__), \
    INIT_ID(__copy__), \
    INIT_ID(__ctypes_from_outparam__), \
    INIT_ID(__del__), \
    INIT_ID(__delattr__), \
    INIT_ID(__delete__), \
    INIT_ID(__delitem__), \
    INIT_ID(__dict__), \
    INIT_ID(__dictoffset__), \
    INIT_ID(__dir__), \
    INIT_ID(__divmod__), \
    INIT_ID(__doc__), \
    INIT_ID(__enter__), \
    INIT_ID(__eq__), \
    INIT_ID(__exit__), \
    INIT_ID(__file__), \
    INIT_ID(__float__), \
    INIT_ID(__floordiv__), \
    INIT_ID(__format__), \
    INIT_ID(__fspath__), \
    INIT_ID(__ge__), \
    INIT_ID(__get__), \
    INIT_ID(__getattr__), \
    INIT_ID(__getattribute__), \
    INIT_ID(__getinitargs__), \
    INIT_ID(__getitem__), \
    INIT_ID(__getnewargs__), \
    INIT_ID(__getnewargs_ex__), \
    INIT_ID(__getstate__), \
    INIT_ID(__gt__), \
    INIT_ID(__hash__), \
    INIT_ID(__iadd__), \
    INIT_ID(__iand__), \
    INIT_ID(__ifloordiv__), \
    INIT_ID(__ilshift__), \
    INIT_ID(__imatmul__), \
    INIT_ID(__imod__), \
    INIT_ID(__import__), \
    INIT_ID(__imul__), \
    INIT_ID(__index__), \
    INIT_ID(__init__), \
    INIT_ID(__init_subclass__), \
    INIT_ID(__instancecheck__), \
    INIT_ID(__int__), \
    INIT_ID(__invert__), \
    INIT_ID(__ior__), \
    INIT_ID(__ipow__), \
    INIT_ID(__irshift__), \
    INIT_ID(__isabstractmethod__), \
    INIT_ID(__isub__), \
    INIT_ID(__iter__), \
    INIT_ID(__itruediv__), \
    INIT_ID(__ixor__), \
    INIT_ID(__le__), \
    INIT_ID(__len__), \
    INIT_ID(__length_hint__), \
    INIT_ID(__lltrace__), \
    INIT_ID(__loader__), \
    INIT_ID(__lshift__), \
    INIT_ID(__lt__), \
    INIT_ID(__main__), \
    INIT_ID(__matmul__), \
    INIT_ID(__missing__), \
    INIT_ID(__mod__), \
    INIT_ID(__module__), \
    INIT_ID(__mro_entries__), \
    INIT_ID(__mul__), \
    INIT_ID(__name__), \
    INIT_ID(__ne__), \
    INIT_ID(__neg__), \
    INIT_ID(__new__), \
    INIT_ID(__newobj__), \
    INIT_ID(__newobj_ex__), \
    INIT_ID(__next__), \
    INIT_ID(__notes__), \
    INIT_ID(__or__), \
    INIT_ID(__orig_class__), \
    INIT_ID(__origin__), \
    INIT_ID(__package__), \
    INIT_ID(__parameters__), \
    INIT_ID(__path__), \
    INIT_ID(__pos__), \
    INIT_ID(__pow__), \
    INIT_ID(__prepare__), \
    INIT_ID(__qualname__), \
    INIT_ID(__radd__), \
    INIT_ID(__rand__), \
    INIT_ID(__rdivmod__), \
    INIT_ID(__reduce__), \
    INIT_ID(__reduce_ex__), \
    INIT_ID(__release_buffer__), \
    INIT_ID(__repr__), \
    INIT_ID(__reversed__), \
    INIT_ID(__rfloordiv__), \
    INIT_ID(__rlshift__), \
    INIT_ID(__rmatmul__), \
    INIT_ID(__rmod__), \
    INIT_ID(__rmul__), \
    INIT_ID(__ror__), \
    INIT_ID(__round__), \
    INIT_ID(__rpow__), \
    INIT_ID(__rrshift__), \
    INIT_ID(__rshift__), \
    INIT_ID(__rsub__), \
    INIT_ID(__rtruediv__), \
    INIT_ID(__rxor__), \
    INIT_ID(__set__), \
    INIT_ID(__set_name__), \
    INIT_ID(__setattr__), \
    INIT_ID(__setitem__), \
    INIT_ID(__setstate__), \
    INIT_ID(__sizeof__), \
    INIT_ID(__slotnames__), \
    INIT_ID(__slots__), \
    INIT_ID(__spec__), \
    INIT_ID(__str__), \
    INIT_ID(__sub__), \
    INIT_ID(__subclasscheck__), \
    INIT_ID(__subclasshook__), \
    INIT_ID(__truediv__), \
    INIT_ID(__trunc__), \
    INIT_ID(__type_params__), \
    INIT_ID(__typing_is_unpacked_typevartuple__), \
    INIT_ID(__typing_prepare_subst__), \
    INIT_ID(__typing_subst__), \
    INIT_ID(__typing_unpacked_tuple_args__), \
    INIT_ID(__warningregistry__), \
    INIT_ID(__weaklistoffset__), \
    INIT_ID(__weakref__), \
    INIT_ID(__xor__), \
    INIT_ID(_abc_impl), \
    INIT_ID(_abstract_), \
    INIT_ID(_active), \
    INIT_ID(_annotation), \
    INIT_ID(_anonymous_), \
    INIT_ID(_argtypes_), \
    INIT_ID(_as_parameter_), \
    INIT_ID(_asyncio_future_blocking), \
    INIT_ID(_blksize), \
    INIT_ID(_bootstrap), \
    INIT_ID(_check_retval_), \
    INIT_ID(_dealloc_warn), \
    INIT_ID(_feature_version), \
    INIT_ID(_fields_), \
    INIT_ID(_finalizing), \
    INIT_ID(_find_and_load), \
    INIT_ID(_fix_up_module), \
    INIT_ID(_flags_), \
    INIT_ID(_get_sourcefile), \
    INIT_ID(_handle_fromlist), \
    INIT_ID(_initializing), \
    INIT_ID(_io), \
    INIT_ID(_is_text_encoding), \
    INIT_ID(_length_), \
    INIT_ID(_limbo), \
    INIT_ID(_lock_unlock_module), \
    INIT_ID(_loop), \
    INIT_ID(_needs_com_addref_), \
    INIT_ID(_only_immortal), \
    INIT_ID(_pack_), \
    INIT_ID(_restype_), \
    INIT_ID(_showwarnmsg), \
    INIT_ID(_shutdown), \
    INIT_ID(_slotnames), \
    INIT_ID(_strptime_datetime), \
    INIT_ID(_swappedbytes_), \
    INIT_ID(_type_), \
    INIT_ID(_uninitialized_submodules), \
    INIT_ID(_warn_unawaited_coroutine), \
    INIT_ID(_xoptions), \
    INIT_ID(abs_tol), \
    INIT_ID(access), \
    INIT_ID(add), \
    INIT_ID(add_done_callback), \
    INIT_ID(after_in_child), \
    INIT_ID(after_in_parent), \
    INIT_ID(aggregate_class), \
    INIT_ID(alias), \
    INIT_ID(append), \
    INIT_ID(arg), \
    INIT_ID(argdefs), \
    INIT_ID(args), \
    INIT_ID(arguments), \
    INIT_ID(argv), \
    INIT_ID(as_integer_ratio), \
    INIT_ID(ast), \
    INIT_ID(attribute), \
    INIT_ID(authorizer_callback), \
    INIT_ID(autocommit), \
    INIT_ID(backtick), \
    INIT_ID(base), \
    INIT_ID(before), \
    INIT_ID(big), \
    INIT_ID(binary_form), \
    INIT_ID(block), \
    INIT_ID(bound), \
    INIT_ID(buffer), \
    INIT_ID(buffer_callback), \
    INIT_ID(buffer_size), \
    INIT_ID(buffering), \
    INIT_ID(buffers), \
    INIT_ID(bufsize), \
    INIT_ID(builtins), \
    INIT_ID(byteorder), \
    INIT_ID(bytes), \
    INIT_ID(bytes_per_sep), \
    INIT_ID(c_call), \
    INIT_ID(c_exception), \
    INIT_ID(c_return), \
    INIT_ID(cached_statements), \
    INIT_ID(cadata), \
    INIT_ID(cafile), \
    INIT_ID(call), \
    INIT_ID(call_exception_handler), \
    INIT_ID(call_soon), \
    INIT_ID(cancel), \
    INIT_ID(capath), \
    INIT_ID(category), \
    INIT_ID(cb_type), \
    INIT_ID(certfile), \
    INIT_ID(check_same_thread), \
    INIT_ID(clear), \
    INIT_ID(close), \
    INIT_ID(closed), \
    INIT_ID(closefd), \
    INIT_ID(closure), \
    INIT_ID(co_argcount), \
    INIT_ID(co_cellvars), \
    INIT_ID(co_code), \
    INIT_ID(co_consts), \
    INIT_ID(co_exceptiontable), \
    INIT_ID(co_filename), \
    INIT_ID(co_firstlineno), \
    INIT_ID(co_flags), \
    INIT_ID(co_freevars), \
    INIT_ID(co_kwonlyargcount), \
    INIT_ID(co_linetable), \
    INIT_ID(co_name), \
    INIT_ID(co_names), \
    INIT_ID(co_nlocals), \
    INIT_ID(co_posonlyargcount), \
    INIT_ID(co_qualname), \
    INIT_ID(co_stacksize), \
    INIT_ID(co_varnames), \
    INIT_ID(code), \
    INIT_ID(command), \
    INIT_ID(comment_factory), \
    INIT_ID(compile_mode), \
    INIT_ID(consts), \
    INIT_ID(context), \
    INIT_ID(contravariant), \
    INIT_ID(cookie), \
    INIT_ID(copy), \
    INIT_ID(copyreg), \
    INIT_ID(coro), \
    INIT_ID(count), \
    INIT_ID(covariant), \
    INIT_ID(cwd), \
    INIT_ID(data), \
    INIT_ID(database), \
    INIT_ID(decode), \
    INIT_ID(decoder), \
    INIT_ID(default), \
    INIT_ID(defaultaction), \
    INIT_ID(delete), \
    INIT_ID(depth), \
    INIT_ID(detect_types), \
    INIT_ID(deterministic), \
    INIT_ID(device), \
    INIT_ID(dict), \
    INIT_ID(dictcomp), \
    INIT_ID(difference_update), \
    INIT_ID(digest), \
    INIT_ID(digest_size), \
    INIT_ID(digestmod), \
    INIT_ID(dir_fd), \
    INIT_ID(discard), \
    INIT_ID(dispatch_table), \
    INIT_ID(displayhook), \
    INIT_ID(dklen), \
    INIT_ID(doc), \
    INIT_ID(dont_inherit), \
    INIT_ID(dst), \
    INIT_ID(dst_dir_fd), \
    INIT_ID(duration), \
    INIT_ID(eager_start), \
    INIT_ID(effective_ids), \
    INIT_ID(element_factory), \
    INIT_ID(encode), \
    INIT_ID(encoding), \
    INIT_ID(end), \
    INIT_ID(end_lineno), \
    INIT_ID(end_offset), \
    INIT_ID(endpos), \
    INIT_ID(entrypoint), \
    INIT_ID(env), \
    INIT_ID(errors), \
    INIT_ID(event), \
    INIT_ID(eventmask), \
    INIT_ID(exc_type), \
    INIT_ID(exc_value), \
    INIT_ID(excepthook), \
    INIT_ID(exception), \
    INIT_ID(existing_file_name), \
    INIT_ID(exp), \
    INIT_ID(extend), \
    INIT_ID(extra_tokens), \
    INIT_ID(facility), \
    INIT_ID(factory), \
    INIT_ID(false), \
    INIT_ID(family), \
    INIT_ID(fanout), \
    INIT_ID(fd), \
    INIT_ID(fd2), \
    INIT_ID(fdel), \
    INIT_ID(fget), \
    INIT_ID(file), \
    INIT_ID(file_actions), \
    INIT_ID(filename), \
    INIT_ID(fileno), \
    INIT_ID(filepath), \
    INIT_ID(fillvalue), \
    INIT_ID(filters), \
    INIT_ID(final), \
    INIT_ID(find_class), \
    INIT_ID(fix_imports), \
    INIT_ID(flags), \
    INIT_ID(flush), \
    INIT_ID(follow_symlinks), \
    INIT_ID(format), \
    INIT_ID(frequency), \
    INIT_ID(from_param), \
    INIT_ID(fromlist), \
    INIT_ID(fromtimestamp), \
    INIT_ID(fromutc), \
    INIT_ID(fset), \
    INIT_ID(func), \
    INIT_ID(future), \
    INIT_ID(generation), \
    INIT_ID(genexpr), \
    INIT_ID(get), \
    INIT_ID(get_debug), \
    INIT_ID(get_event_loop), \
    INIT_ID(get_loop), \
    INIT_ID(get_source), \
    INIT_ID(getattr), \
    INIT_ID(getstate), \
    INIT_ID(gid), \
    INIT_ID(globals), \
    INIT_ID(groupindex), \
    INIT_ID(groups), \
    INIT_ID(handle), \
    INIT_ID(hash_name), \
    INIT_ID(header), \
    INIT_ID(headers), \
    INIT_ID(hi), \
    INIT_ID(hook), \
    INIT_ID(id), \
    INIT_ID(ident), \
    INIT_ID(ignore), \
    INIT_ID(imag), \
    INIT_ID(importlib), \
    INIT_ID(in_fd), \
    INIT_ID(incoming), \
    INIT_ID(indexgroup), \
    INIT_ID(inf), \
    INIT_ID(infer_variance), \
    INIT_ID(inheritable), \
    INIT_ID(initial), \
    INIT_ID(initial_bytes), \
    INIT_ID(initial_value), \
    INIT_ID(initval), \
    INIT_ID(inner_size), \
    INIT_ID(input), \
    INIT_ID(insert_comments), \
    INIT_ID(insert_pis), \
    INIT_ID(instructions), \
    INIT_ID(intern), \
    INIT_ID(intersection), \
    INIT_ID(is_running), \
    INIT_ID(isatty), \
    INIT_ID(isinstance), \
    INIT_ID(isoformat), \
    INIT_ID(isolation_level), \
    INIT_ID(istext), \
    INIT_ID(item), \
    INIT_ID(items), \
    INIT_ID(iter), \
    INIT_ID(iterable), \
    INIT_ID(iterations), \
    INIT_ID(join), \
    INIT_ID(jump), \
    INIT_ID(keepends), \
    INIT_ID(key), \
    INIT_ID(keyfile), \
    INIT_ID(keys), \
    INIT_ID(kind), \
    INIT_ID(kw), \
    INIT_ID(kw1), \
    INIT_ID(kw2), \
    INIT_ID(lambda), \
    INIT_ID(last), \
    INIT_ID(last_exc), \
    INIT_ID(last_node), \
    INIT_ID(last_traceback), \
    INIT_ID(last_type), \
    INIT_ID(last_value), \
    INIT_ID(latin1), \
    INIT_ID(leaf_size), \
    INIT_ID(len), \
    INIT_ID(length), \
    INIT_ID(level), \
    INIT_ID(limit), \
    INIT_ID(line), \
    INIT_ID(line_buffering), \
    INIT_ID(lineno), \
    INIT_ID(listcomp), \
    INIT_ID(little), \
    INIT_ID(lo), \
    INIT_ID(locale), \
    INIT_ID(locals), \
    INIT_ID(logoption), \
    INIT_ID(loop), \
    INIT_ID(mapping), \
    INIT_ID(match), \
    INIT_ID(max_length), \
    INIT_ID(maxdigits), \
    INIT_ID(maxevents), \
    INIT_ID(maxmem), \
    INIT_ID(maxsplit), \
    INIT_ID(maxvalue), \
    INIT_ID(memLevel), \
    INIT_ID(memlimit), \
    INIT_ID(message), \
    INIT_ID(metaclass), \
    INIT_ID(metadata), \
    INIT_ID(method), \
    INIT_ID(mod), \
    INIT_ID(mode), \
    INIT_ID(module), \
    INIT_ID(module_globals), \
    INIT_ID(modules), \
    INIT_ID(mro), \
    INIT_ID(msg), \
    INIT_ID(mycmp), \
    INIT_ID(n_arg), \
    INIT_ID(n_fields), \
    INIT_ID(n_sequence_fields), \
    INIT_ID(n_unnamed_fields), \
    INIT_ID(name), \
    INIT_ID(name_from), \
    INIT_ID(namespace_separator), \
    INIT_ID(namespaces), \
    INIT_ID(narg), \
    INIT_ID(ndigits), \
    INIT_ID(new_file_name), \
    INIT_ID(new_limit), \
    INIT_ID(newline), \
    INIT_ID(newlines), \
    INIT_ID(next), \
    INIT_ID(nlocals), \
    INIT_ID(node_depth), \
    INIT_ID(node_offset), \
    INIT_ID(ns), \
    INIT_ID(nstype), \
    INIT_ID(nt), \
    INIT_ID(null), \
    INIT_ID(number), \
    INIT_ID(obj), \
    INIT_ID(object), \
    INIT_ID(offset), \
    INIT_ID(offset_dst), \
    INIT_ID(offset_src), \
    INIT_ID(on_type_read), \
    INIT_ID(onceregistry), \
    INIT_ID(only_keys), \
    INIT_ID(oparg), \
    INIT_ID(opcode), \
    INIT_ID(open), \
    INIT_ID(opener), \
    INIT_ID(operation), \
    INIT_ID(optimize), \
    INIT_ID(options), \
    INIT_ID(order), \
    INIT_ID(origin), \
    INIT_ID(out_fd), \
    INIT_ID(outgoing), \
    INIT_ID(overlapped), \
    INIT_ID(owner), \
    INIT_ID(pages), \
    INIT_ID(parent), \
    INIT_ID(password), \
    INIT_ID(path), \
    INIT_ID(pattern), \
    INIT_ID(peek), \
    INIT_ID(persistent_id), \
    INIT_ID(persistent_load), \
    INIT_ID(person), \
    INIT_ID(pi_factory), \
    INIT_ID(pid), \
    INIT_ID(policy), \
    INIT_ID(pos), \
    INIT_ID(pos1), \
    INIT_ID(pos2), \
    INIT_ID(posix), \
    INIT_ID(print_file_and_line), \
    INIT_ID(priority), \
    INIT_ID(progress), \
    INIT_ID(progress_handler), \
    INIT_ID(progress_routine), \
    INIT_ID(proto), \
    INIT_ID(protocol), \
    INIT_ID(ps1), \
    INIT_ID(ps2), \
    INIT_ID(query), \
    INIT_ID(quotetabs), \
    INIT_ID(raw), \
    INIT_ID(read), \
    INIT_ID(read1), \
    INIT_ID(readable), \
    INIT_ID(readall), \
    INIT_ID(readinto), \
    INIT_ID(readinto1), \
    INIT_ID(readline), \
    INIT_ID(readonly), \
    INIT_ID(real), \
    INIT_ID(reducer_override), \
    INIT_ID(registry), \
    INIT_ID(rel_tol), \
    INIT_ID(release), \
    INIT_ID(reload), \
    INIT_ID(repl), \
    INIT_ID(replace), \
    INIT_ID(reserved), \
    INIT_ID(reset), \
    INIT_ID(resetids), \
    INIT_ID(return), \
    INIT_ID(reverse), \
    INIT_ID(reversed), \
    INIT_ID(salt), \
    INIT_ID(sched_priority), \
    INIT_ID(scheduler), \
    INIT_ID(seek), \
    INIT_ID(seekable), \
    INIT_ID(selectors), \
    INIT_ID(self), \
    INIT_ID(send), \
    INIT_ID(sep), \
    INIT_ID(sequence), \
    INIT_ID(server_hostname), \
    INIT_ID(server_side), \
    INIT_ID(session), \
    INIT_ID(setcomp), \
    INIT_ID(setpgroup), \
    INIT_ID(setsid), \
    INIT_ID(setsigdef), \
    INIT_ID(setsigmask), \
    INIT_ID(setstate), \
    INIT_ID(shape), \
    INIT_ID(show_cmd), \
    INIT_ID(signed), \
    INIT_ID(size), \
    INIT_ID(sizehint), \
    INIT_ID(skip_file_prefixes), \
    INIT_ID(sleep), \
    INIT_ID(sock), \
    INIT_ID(sort), \
    INIT_ID(sound), \
    INIT_ID(source), \
    INIT_ID(source_traceback), \
    INIT_ID(spam), \
    INIT_ID(src), \
    INIT_ID(src_dir_fd), \
    INIT_ID(stacklevel), \
    INIT_ID(start), \
    INIT_ID(statement), \
    INIT_ID(status), \
    INIT_ID(stderr), \
    INIT_ID(stdin), \
    INIT_ID(stdout), \
    INIT_ID(step), \
    INIT_ID(steps), \
    INIT_ID(store_name), \
    INIT_ID(strategy), \
    INIT_ID(strftime), \
    INIT_ID(strict), \
    INIT_ID(strict_mode), \
    INIT_ID(string), \
    INIT_ID(sub_key), \
    INIT_ID(symmetric_difference_update), \
    INIT_ID(tabsize), \
    INIT_ID(tag), \
    INIT_ID(target), \
    INIT_ID(target_is_directory), \
    INIT_ID(task), \
    INIT_ID(tb_frame), \
    INIT_ID(tb_lasti), \
    INIT_ID(tb_lineno), \
    INIT_ID(tb_next), \
    INIT_ID(tell), \
    INIT_ID(template), \
    INIT_ID(term), \
    INIT_ID(text), \
    INIT_ID(threading), \
    INIT_ID(throw), \
    INIT_ID(timeout), \
    INIT_ID(times), \
    INIT_ID(timetuple), \
    INIT_ID(top), \
    INIT_ID(trace_callback), \
    INIT_ID(traceback), \
    INIT_ID(trailers), \
    INIT_ID(translate), \
    INIT_ID(true), \
    INIT_ID(truncate), \
    INIT_ID(twice), \
    INIT_ID(txt), \
    INIT_ID(type), \
    INIT_ID(type_params), \
    INIT_ID(tz), \
    INIT_ID(tzname), \
    INIT_ID(uid), \
    INIT_ID(unlink), \
    INIT_ID(unraisablehook), \
    INIT_ID(uri), \
    INIT_ID(usedforsecurity), \
    INIT_ID(value), \
    INIT_ID(values), \
    INIT_ID(version), \
    INIT_ID(volume), \
    INIT_ID(warnings), \
    INIT_ID(warnoptions), \
    INIT_ID(wbits), \
    INIT_ID(week), \
    INIT_ID(weekday), \
    INIT_ID(which), \
    INIT_ID(who), \
    INIT_ID(withdata), \
    INIT_ID(writable), \
    INIT_ID(write), \
    INIT_ID(write_through), \
    INIT_ID(year), \
    INIT_ID(zdict), \
}

#define _Py_str_ascii_INIT { \
    _PyASCIIObject_INIT("\x00"), \
    _PyASCIIObject_INIT("\x01"), \
    _PyASCIIObject_INIT("\x02"), \
    _PyASCIIObject_INIT("\x03"), \
    _PyASCIIObject_INIT("\x04"), \
    _PyASCIIObject_INIT("\x05"), \
    _PyASCIIObject_INIT("\x06"), \
    _PyASCIIObject_INIT("\x07"), \
    _PyASCIIObject_INIT("\x08"), \
    _PyASCIIObject_INIT("\x09"), \
    _PyASCIIObject_INIT("\x0a"), \
    _PyASCIIObject_INIT("\x0b"), \
    _PyASCIIObject_INIT("\x0c"), \
    _PyASCIIObject_INIT("\x0d"), \
    _PyASCIIObject_INIT("\x0e"), \
    _PyASCIIObject_INIT("\x0f"), \
    _PyASCIIObject_INIT("\x10"), \
    _PyASCIIObject_INIT("\x11"), \
    _PyASCIIObject_INIT("\x12"), \
    _PyASCIIObject_INIT("\x13"), \
    _PyASCIIObject_INIT("\x14"), \
    _PyASCIIObject_INIT("\x15"), \
    _PyASCIIObject_INIT("\x16"), \
    _PyASCIIObject_INIT("\x17"), \
    _PyASCIIObject_INIT("\x18"), \
    _PyASCIIObject_INIT("\x19"), \
    _PyASCIIObject_INIT("\x1a"), \
    _PyASCIIObject_INIT("\x1b"), \
    _PyASCIIObject_INIT("\x1c"), \
    _PyASCIIObject_INIT("\x1d"), \
    _PyASCIIObject_INIT("\x1e"), \
    _PyASCIIObject_INIT("\x1f"), \
    _PyASCIIObject_INIT("\x20"), \
    _PyASCIIObject_INIT("\x21"), \
    _PyASCIIObject_INIT("\x22"), \
    _PyASCIIObject_INIT("\x23"), \
    _PyASCIIObject_INIT("\x24"), \
    _PyASCIIObject_INIT("\x25"), \
    _PyASCIIObject_INIT("\x26"), \
    _PyASCIIObject_INIT("\x27"), \
    _PyASCIIObject_INIT("\x28"), \
    _PyASCIIObject_INIT("\x29"), \
    _PyASCIIObject_INIT("\x2a"), \
    _PyASCIIObject_INIT("\x2b"), \
    _PyASCIIObject_INIT("\x2c"), \
    _PyASCIIObject_INIT("\x2d"), \
    _PyASCIIObject_INIT("\x2e"), \
    _PyASCIIObject_INIT("\x2f"), \
    _PyASCIIObject_INIT("\x30"), \
    _PyASCIIObject_INIT("\x31"), \
    _PyASCIIObject_INIT("\x32"), \
    _PyASCIIObject_INIT("\x33"), \
    _PyASCIIObject_INIT("\x34"), \
    _PyASCIIObject_INIT("\x35"), \
    _PyASCIIObject_INIT("\x36"), \
    _PyASCIIObject_INIT("\x37"), \
    _PyASCIIObject_INIT("\x38"), \
    _PyASCIIObject_INIT("\x39"), \
    _PyASCIIObject_INIT("\x3a"), \
    _PyASCIIObject_INIT("\x3b"), \
    _PyASCIIObject_INIT("\x3c"), \
    _PyASCIIObject_INIT("\x3d"), \
    _PyASCIIObject_INIT("\x3e"), \
    _PyASCIIObject_INIT("\x3f"), \
    _PyASCIIObject_INIT("\x40"), \
    _PyASCIIObject_INIT("\x41"), \
    _PyASCIIObject_INIT("\x42"), \
    _PyASCIIObject_INIT("\x43"), \
    _PyASCIIObject_INIT("\x44"), \
    _PyASCIIObject_INIT("\x45"), \
    _PyASCIIObject_INIT("\x46"), \
    _PyASCIIObject_INIT("\x47"), \
    _PyASCIIObject_INIT("\x48"), \
    _PyASCIIObject_INIT("\x49"), \
    _PyASCIIObject_INIT("\x4a"), \
    _PyASCIIObject_INIT("\x4b"), \
    _PyASCIIObject_INIT("\x4c"), \
    _PyASCIIObject_INIT("\x4d"), \
    _PyASCIIObject_INIT("\x4e"), \
    _PyASCIIObject_INIT("\x4f"), \
    _PyASCIIObject_INIT("\x50"), \
    _PyASCIIObject_INIT("\x51"), \
    _PyASCIIObject_INIT("\x52"), \
    _PyASCIIObject_INIT("\x53"), \
    _PyASCIIObject_INIT("\x54"), \
    _PyASCIIObject_INIT("\x55"), \
    _PyASCIIObject_INIT("\x56"), \
    _PyASCIIObject_INIT("\x57"), \
    _PyASCIIObject_INIT("\x58"), \
    _PyASCIIObject_INIT("\x59"), \
    _PyASCIIObject_INIT("\x5a"), \
    _PyASCIIObject_INIT("\x5b"), \
    _PyASCIIObject_INIT("\x5c"), \
    _PyASCIIObject_INIT("\x5d"), \
    _PyASCIIObject_INIT("\x5e"), \
    _PyASCIIObject_INIT("\x5f"), \
    _PyASCIIObject_INIT("\x60"), \
    _PyASCIIObject_INIT("\x61"), \
    _PyASCIIObject_INIT("\x62"), \
    _PyASCIIObject_INIT("\x63"), \
    _PyASCIIObject_INIT("\x64"), \
    _PyASCIIObject_INIT("\x65"), \
    _PyASCIIObject_INIT("\x66"), \
    _PyASCIIObject_INIT("\x67"), \
    _PyASCIIObject_INIT("\x68"), \
    _PyASCIIObject_INIT("\x69"), \
    _PyASCIIObject_INIT("\x6a"), \
    _PyASCIIObject_INIT("\x6b"), \
    _PyASCIIObject_INIT("\x6c"), \
    _PyASCIIObject_INIT("\x6d"), \
    _PyASCIIObject_INIT("\x6e"), \
    _PyASCIIObject_INIT("\x6f"), \
    _PyASCIIObject_INIT("\x70"), \
    _PyASCIIObject_INIT("\x71"), \
    _PyASCIIObject_INIT("\x72"), \
    _PyASCIIObject_INIT("\x73"), \
    _PyASCIIObject_INIT("\x74"), \
    _PyASCIIObject_INIT("\x75"), \
    _PyASCIIObject_INIT("\x76"), \
    _PyASCIIObject_INIT("\x77"), \
    _PyASCIIObject_INIT("\x78"), \
    _PyASCIIObject_INIT("\x79"), \
    _PyASCIIObject_INIT("\x7a"), \
    _PyASCIIObject_INIT("\x7b"), \
    _PyASCIIObject_INIT("\x7c"), \
    _PyASCIIObject_INIT("\x7d"), \
    _PyASCIIObject_INIT("\x7e"), \
    _PyASCIIObject_INIT("\x7f"), \
}

#define _Py_str_latin1_INIT { \
    _PyUnicode_LATIN1_INIT("\x80", "\xc2\x80"), \
    _PyUnicode_LATIN1_INIT("\x81", "\xc2\x81"), \
    _PyUnicode_LATIN1_INIT("\x82", "\xc2\x82"), \
    _PyUnicode_LATIN1_INIT("\x83", "\xc2\x83"), \
    _PyUnicode_LATIN1_INIT("\x84", "\xc2\x84"), \
    _PyUnicode_LATIN1_INIT("\x85", "\xc2\x85"), \
    _PyUnicode_LATIN1_INIT("\x86", "\xc2\x86"), \
    _PyUnicode_LATIN1_INIT("\x87", "\xc2\x87"), \
    _PyUnicode_LATIN1_INIT("\x88", "\xc2\x88"), \
    _PyUnicode_LATIN1_INIT("\x89", "\xc2\x89"), \
    _PyUnicode_LATIN1_INIT("\x8a", "\xc2\x8a"), \
    _PyUnicode_LATIN1_INIT("\x8b", "\xc2\x8b"), \
    _PyUnicode_LATIN1_INIT("\x8c", "\xc2\x8c"), \
    _PyUnicode_LATIN1_INIT("\x8d", "\xc2\x8d"), \
    _PyUnicode_LATIN1_INIT("\x8e", "\xc2\x8e"), \
    _PyUnicode_LATIN1_INIT("\x8f", "\xc2\x8f"), \
    _PyUnicode_LATIN1_INIT("\x90", "\xc2\x90"), \
    _PyUnicode_LATIN1_INIT("\x91", "\xc2\x91"), \
    _PyUnicode_LATIN1_INIT("\x92", "\xc2\x92"), \
    _PyUnicode_LATIN1_INIT("\x93", "\xc2\x93"), \
    _PyUnicode_LATIN1_INIT("\x94", "\xc2\x94"), \
    _PyUnicode_LATIN1_INIT("\x95", "\xc2\x95"), \
    _PyUnicode_LATIN1_INIT("\x96", "\xc2\x96"), \
    _PyUnicode_LATIN1_INIT("\x97", "\xc2\x97"), \
    _PyUnicode_LATIN1_INIT("\x98", "\xc2\x98"), \
    _PyUnicode_LATIN1_INIT("\x99", "\xc2\x99"), \
    _PyUnicode_LATIN1_INIT("\x9a", "\xc2\x9a"), \
    _PyUnicode_LATIN1_INIT("\x9b", "\xc2\x9b"), \
    _PyUnicode_LATIN1_INIT("\x9c", "\xc2\x9c"), \
    _PyUnicode_LATIN1_INIT("\x9d", "\xc2\x9d"), \
    _PyUnicode_LATIN1_INIT("\x9e", "\xc2\x9e"), \
    _PyUnicode_LATIN1_INIT("\x9f", "\xc2\x9f"), \
    _PyUnicode_LATIN1_INIT("\xa0", "\xc2\xa0"), \
    _PyUnicode_LATIN1_INIT("\xa1", "\xc2\xa1"), \
    _PyUnicode_LATIN1_INIT("\xa2", "\xc2\xa2"), \
    _PyUnicode_LATIN1_INIT("\xa3", "\xc2\xa3"), \
    _PyUnicode_LATIN1_INIT("\xa4", "\xc2\xa4"), \
    _PyUnicode_LATIN1_INIT("\xa5", "\xc2\xa5"), \
    _PyUnicode_LATIN1_INIT("\xa6", "\xc2\xa6"), \
    _PyUnicode_LATIN1_INIT("\xa7", "\xc2\xa7"), \
    _PyUnicode_LATIN1_INIT("\xa8", "\xc2\xa8"), \
    _PyUnicode_LATIN1_INIT("\xa9", "\xc2\xa9"), \
    _PyUnicode_LATIN1_INIT("\xaa", "\xc2\xaa"), \
    _PyUnicode_LATIN1_INIT("\xab", "\xc2\xab"), \
    _PyUnicode_LATIN1_INIT("\xac", "\xc2\xac"), \
    _PyUnicode_LATIN1_INIT("\xad", "\xc2\xad"), \
    _PyUnicode_LATIN1_INIT("\xae", "\xc2\xae"), \
    _PyUnicode_LATIN1_INIT("\xaf", "\xc2\xaf"), \
    _PyUnicode_LATIN1_INIT("\xb0", "\xc2\xb0"), \
    _PyUnicode_LATIN1_INIT("\xb1", "\xc2\xb1"), \
    _PyUnicode_LATIN1_INIT("\xb2", "\xc2\xb2"), \
    _PyUnicode_LATIN1_INIT("\xb3", "\xc2\xb3"), \
    _PyUnicode_LATIN1_INIT("\xb4", "\xc2\xb4"), \
    _PyUnicode_LATIN1_INIT("\xb5", "\xc2\xb5"), \
    _PyUnicode_LATIN1_INIT("\xb6", "\xc2\xb6"), \
    _PyUnicode_LATIN1_INIT("\xb7", "\xc2\xb7"), \
    _PyUnicode_LATIN1_INIT("\xb8", "\xc2\xb8"), \
    _PyUnicode_LATIN1_INIT("\xb9", "\xc2\xb9"), \
    _PyUnicode_LATIN1_INIT("\xba", "\xc2\xba"), \
    _PyUnicode_LATIN1_INIT("\xbb", "\xc2\xbb"), \
    _PyUnicode_LATIN1_INIT("\xbc", "\xc2\xbc"), \
    _PyUnicode_LATIN1_INIT("\xbd", "\xc2\xbd"), \
    _PyUnicode_LATIN1_INIT("\xbe", "\xc2\xbe"), \
    _PyUnicode_LATIN1_INIT("\xbf", "\xc2\xbf"), \
    _PyUnicode_LATIN1_INIT("\xc0", "\xc3\x80"), \
    _PyUnicode_LATIN1_INIT("\xc1", "\xc3\x81"), \
    _PyUnicode_LATIN1_INIT("\xc2", "\xc3\x82"), \
    _PyUnicode_LATIN1_INIT("\xc3", "\xc3\x83"), \
    _PyUnicode_LATIN1_INIT("\xc4", "\xc3\x84"), \
    _PyUnicode_LATIN1_INIT("\xc5", "\xc3\x85"), \
    _PyUnicode_LATIN1_INIT("\xc6", "\xc3\x86"), \
    _PyUnicode_LATIN1_INIT("\xc7", "\xc3\x87"), \
    _PyUnicode_LATIN1_INIT("\xc8", "\xc3\x88"), \
    _PyUnicode_LATIN1_INIT("\xc9", "\xc3\x89"), \
    _PyUnicode_LATIN1_INIT("\xca", "\xc3\x8a"), \
    _PyUnicode_LATIN1_INIT("\xcb", "\xc3\x8b"), \
    _PyUnicode_LATIN1_INIT("\xcc", "\xc3\x8c"), \
    _PyUnicode_LATIN1_INIT("\xcd", "\xc3\x8d"), \
    _PyUnicode_LATIN1_INIT("\xce", "\xc3\x8e"), \
    _PyUnicode_LATIN1_INIT("\xcf", "\xc3\x8f"), \
    _PyUnicode_LATIN1_INIT("\xd0", "\xc3\x90"), \
    _PyUnicode_LATIN1_INIT("\xd1", "\xc3\x91"), \
    _PyUnicode_LATIN1_INIT("\xd2", "\xc3\x92"), \
    _PyUnicode_LATIN1_INIT("\xd3", "\xc3\x93"), \
    _PyUnicode_LATIN1_INIT("\xd4", "\xc3\x94"), \
    _PyUnicode_LATIN1_INIT("\xd5", "\xc3\x95"), \
    _PyUnicode_LATIN1_INIT("\xd6", "\xc3\x96"), \
    _PyUnicode_LATIN1_INIT("\xd7", "\xc3\x97"), \
    _PyUnicode_LATIN1_INIT("\xd8", "\xc3\x98"), \
    _PyUnicode_LATIN1_INIT("\xd9", "\xc3\x99"), \
    _PyUnicode_LATIN1_INIT("\xda", "\xc3\x9a"), \
    _PyUnicode_LATIN1_INIT("\xdb", "\xc3\x9b"), \
    _PyUnicode_LATIN1_INIT("\xdc", "\xc3\x9c"), \
    _PyUnicode_LATIN1_INIT("\xdd", "\xc3\x9d"), \
    _PyUnicode_LATIN1_INIT("\xde", "\xc3\x9e"), \
    _PyUnicode_LATIN1_INIT("\xdf", "\xc3\x9f"), \
    _PyUnicode_LATIN1_INIT("\xe0", "\xc3\xa0"), \
    _PyUnicode_LATIN1_INIT("\xe1", "\xc3\xa1"), \
    _PyUnicode_LATIN1_INIT("\xe2", "\xc3\xa2"), \
    _PyUnicode_LATIN1_INIT("\xe3", "\xc3\xa3"), \
    _PyUnicode_LATIN1_INIT("\xe4", "\xc3\xa4"), \
    _PyUnicode_LATIN1_INIT("\xe5", "\xc3\xa5"), \
    _PyUnicode_LATIN1_INIT("\xe6", "\xc3\xa6"), \
    _PyUnicode_LATIN1_INIT("\xe7", "\xc3\xa7"), \
    _PyUnicode_LATIN1_INIT("\xe8", "\xc3\xa8"), \
    _PyUnicode_LATIN1_INIT("\xe9", "\xc3\xa9"), \
    _PyUnicode_LATIN1_INIT("\xea", "\xc3\xaa"), \
    _PyUnicode_LATIN1_INIT("\xeb", "\xc3\xab"), \
    _PyUnicode_LATIN1_INIT("\xec", "\xc3\xac"), \
    _PyUnicode_LATIN1_INIT("\xed", "\xc3\xad"), \
    _PyUnicode_LATIN1_INIT("\xee", "\xc3\xae"), \
    _PyUnicode_LATIN1_INIT("\xef", "\xc3\xaf"), \
    _PyUnicode_LATIN1_INIT("\xf0", "\xc3\xb0"), \
    _PyUnicode_LATIN1_INIT("\xf1", "\xc3\xb1"), \
    _PyUnicode_LATIN1_INIT("\xf2", "\xc3\xb2"), \
    _PyUnicode_LATIN1_INIT("\xf3", "\xc3\xb3"), \
    _PyUnicode_LATIN1_INIT("\xf4", "\xc3\xb4"), \
    _PyUnicode_LATIN1_INIT("\xf5", "\xc3\xb5"), \
    _PyUnicode_LATIN1_INIT("\xf6", "\xc3\xb6"), \
    _PyUnicode_LATIN1_INIT("\xf7", "\xc3\xb7"), \
    _PyUnicode_LATIN1_INIT("\xf8", "\xc3\xb8"), \
    _PyUnicode_LATIN1_INIT("\xf9", "\xc3\xb9"), \
    _PyUnicode_LATIN1_INIT("\xfa", "\xc3\xba"), \
    _PyUnicode_LATIN1_INIT("\xfb", "\xc3\xbb"), \
    _PyUnicode_LATIN1_INIT("\xfc", "\xc3\xbc"), \
    _PyUnicode_LATIN1_INIT("\xfd", "\xc3\xbd"), \
    _PyUnicode_LATIN1_INIT("\xfe", "\xc3\xbe"), \
    _PyUnicode_LATIN1_INIT("\xff", "\xc3\xbf"), \
}
/* End auto-generated code */

#ifdef __cplusplus
}
#endif
#endif /* !Py_INTERNAL_RUNTIME_INIT_GENERATED_H */
